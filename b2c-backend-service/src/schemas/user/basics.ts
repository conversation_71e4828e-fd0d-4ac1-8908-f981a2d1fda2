import { z } from 'zod';
import { UUIDSchema } from '@schemas/common/common';

export const BasicProfilesQuerySchema = z.object({
  profileIds: z
    .string()
    .transform((str) => str.split(','))
    .pipe(
      z.array(UUIDSchema).min(1, {
        message: 'At least one valid profile ID is required',
      }),
    ),
});

export const BasicProfilePostQuerySchema = z.object({
  profileId: UUIDSchema,
});

export type BasicProfilesParamsI = z.infer<typeof BasicProfilesQuerySchema>;
