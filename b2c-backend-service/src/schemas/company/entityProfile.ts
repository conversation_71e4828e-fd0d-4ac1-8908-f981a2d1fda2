import { z } from 'zod';
import { UUIDSchema } from '../common/common';
import { EntityTypeE } from '@prisma/postgres';

export const EntityAboutFetchSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });
export type EntityAboutFetchParamsI = z.infer<typeof EntityAboutFetchSchema>;

export const EntityAboutUpdateSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
    name: z.string().min(1).max(255),
    website: z.string().url().max(255).nullable().optional(),
    countryIso2: z.string().length(2).nullable().optional(),
    type: z.nativeEnum(EntityTypeE),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityAboutUpdateParamsI = z.infer<typeof EntityAboutUpdateSchema>;

export const EntityTabsFetchSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityTabsFetchParamsI = z.infer<typeof EntityTabsFetchSchema>;

export const EntityTabsUpdateSchema = z
  .object({
    entityId: UUIDSchema.optional(),
    entityRawDataId: UUIDSchema.optional(),
    peopleTab: z.boolean().optional(),
    alumniTab: z.boolean().optional(),
    jobPostingTab: z.boolean().optional(),
  })
  .refine((data) => data.entityId || data.entityRawDataId, {
    message: 'Either entityId or entityRawDataId must be provided.',
  })
  .refine((data) => !(data.entityId && data.entityRawDataId), {
    message: 'Only one of entityId or entityRawDataId must be provided.',
  });

export type EntityTabsUpdateParamsI = z.infer<typeof EntityTabsUpdateSchema>;
