import { z } from 'zod';
import { POST_MAX_NO_OF_FILES, PostFileExtensionE } from '@consts/feed/post';
import {  CursorPaginationSchema, UUIDSchema } from '@schemas/common/common';

export const EntityPostCreateItemSchema = z.object({
  caption: z.string().max(255).optional(),
  fileUrl: z.string().url(),
  extension: PostFileExtensionE,
});

export const EntityPostCreateOneSchema = z.object({
  caption: z.string().min(1).max(2000),
  entityId: UUIDSchema.optional(),
  entityRawDataId: UUIDSchema.optional(),
  files: z.array(EntityPostCreateItemSchema).max(POST_MAX_NO_OF_FILES).optional(),
}).superRefine((data, ctx) => {
  if (!data.entityId && !data.entityRawDataId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Either entityId or entityRawDataId must be provided',
    });
  }
  if (!data.caption && !data.files?.length) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Either caption or files must be provided',
    });
  }
});

export const EntityPostFetchManySchema = CursorPaginationSchema.extend({
  entityId: UUIDSchema.optional(),
  entityRawDataId: UUIDSchema.optional(),
});


export type EntityPostCreateItemI = z.infer<typeof EntityPostCreateItemSchema>;
export type EntityPostCreateOneI = z.infer<typeof EntityPostCreateOneSchema>;
export type EntityPostFetchManyI = z.infer<typeof EntityPostFetchManySchema>;
