import AppError from '@classes/AppError';
import s3 from '@config/s3';
import { ACLI, FileExtensionI, FileTypeE, FileTypeValueI, ParentFolderE } from '@consts/storage/storage';
import { DeleteFileI, UploadFileBulkI, UploadFileOneI } from '@interfaces/storage/storage';
import { PresignedURLItemI, PresignedURLItemResultI, PresignedURLsI } from '@schemas/storage/storage';
import { processInBatches } from '@utils/data/concurrency';
import { DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { ENV } from '@consts/common/env';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
export const CoreStorageModule = {
  presignedURL: async ({ folder, extension }: PresignedURLItemI): Promise<PresignedURLItemResultI> => {
    switch (folder) {
      case 'POST':
        if (!(['webp', 'jpeg', 'jpg', 'mp4'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'FORUM':
        if (!(['webp', 'jpeg', 'jpg', 'pdf', 'xls', 'xlsx', 'mp4'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'AVATAR':
        if (!(['webp', 'jpeg', 'jpg', 'pdf', ''] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'CERTIFICATION':
        if (!(['webp', 'jpeg', 'jpg', 'pdf'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'DOCUMENTATION':
        if (!(['webp', 'jpeg', 'jpg', 'pdf'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      case 'CHAT':
        if (!(['webp', 'jpeg', 'jpg', 'mp3', 'mp4', 'm4a', 'pdf'] as FileExtensionI[]).includes(extension)) {
          throw new AppError('STRG002');
        }
        break;
      default:
        throw new AppError('STRG002');
    }
    const Bucket = folder;
    const fileName = `${crypto.randomUUID()}.${extension}`;

    const command = new PutObjectCommand({
      Bucket,
      Key: fileName,
      ContentType: FileTypeE[extension].mime,
      ACL: 'public-read',
    });
    const uploadUrl = await getSignedUrl(s3, command, { expiresIn: ENV.DO_SIGNED_URL_EXPIRY_S });
    const accessUrl = `${ENV.DO_SPACES_CDN_ENDPOINT}/${folder}/${fileName}`;
    const result: PresignedURLItemResultI = {
      extension,
      uploadUrl,
      accessUrl,
    };
    return result;
  },
  presignedURLsBulk: async ({ extensions, folder }: PresignedURLsI): Promise<PresignedURLItemResultI[]> => {
    const presignedURsResult: PresignedURLItemResultI[] = await Promise.all(
      extensions.map((extension) => CoreStorageModule.presignedURL({ extension, folder })),
    );
    return presignedURsResult;
  },
  uploadFilesBulk: async ({ files, parentFolder }: UploadFileBulkI): Promise<string[]> => {
    const output: string[] = (await processInBatches(
      files.map((file) => ({ file, parentFolder: parentFolder }) as UploadFileOneI),
      CoreStorageModule.uploadFileOne,
      20,
    )) as string[];
    return output;
  },
  /**
   *
   * @param param
   *
   * File path (URL):  /<parentFolder>/<fileName>.<fileExtension>
   */
  uploadFileOne: async ({ file, parentFolder }: UploadFileOneI): Promise<string> => {
    try {
      switch (parentFolder) {
        case 'DOCUMENTATION': {
          switch (file.mimetype) {
            case FileTypeE.pdf.mime: {
              break;
            }
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'CERTIFICATION': {
          switch (file.mimetype) {
            case FileTypeE.pdf.mime: {
              break;
            }
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'AVATAR': {
          switch (file.mimetype) {
            case FileTypeE.webp.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
        case 'POST': {
          switch (file.mimetype) {
            case FileTypeE.webp.mime: {
              break;
            }
            case FileTypeE.mp4.mime: {
              break;
            }
            default: {
              throw new AppError('STRG002');
            }
          }
          break;
        }
      }
      const fileType: FileTypeValueI = FileTypeE[file.mimetype];
      const filePath = `${parentFolder}/${crypto.randomUUID()}.${fileType.extension}`;
      const body = await file.toBuffer();
      await s3.send(
        new PutObjectCommand({
          Bucket: ENV.DO_SPACES,
          Key: filePath,
          ACL: 'public-read' as ACLI,
          Body: body,
          ContentType: fileType.mime,
        }),
      );
      const url = `${ENV.DO_SPACES_ENDPOINT}/${filePath}`;
      return url;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('STRG001');
    }
  },
  /**
   * Extract bucket and key from file URL
   * @param fileUrl - Full file URL or relative path
   * @returns Object with bucket and key
   */
  extractBucketAndKey: (fileUrl: string): { bucket: string; key: string } => {
    try {
      if (fileUrl.includes(ENV.DO_SPACES_CDN_ENDPOINT)) {
        const urlPath = fileUrl.replace(ENV.DO_SPACES_CDN_ENDPOINT + '/', '');
        const [bucket, ...keyParts] = urlPath.split('/');
        return { bucket, key: keyParts.join('/') };
      }

      if (fileUrl.includes(ENV.DO_SPACES_ENDPOINT)) {
        const urlPath = fileUrl.replace(ENV.DO_SPACES_ENDPOINT + '/', '');
        const [bucket, ...keyParts] = urlPath.split('/');
        return { bucket, key: keyParts.join('/') };
      }

      const [bucket, ...keyParts] = fileUrl.split('/');
      return { bucket, key: keyParts.join('/') };
    } catch (_error) {
      throw new AppError('STRG005');
    }
  },

  /**
   * Delete file from storage
   * @param fileUrl - The URL of the file to delete (can be full URL or relative path)
   */
  deleteFile: async ({ fileUrl }: DeleteFileI): Promise<void> => {
    try {
      const { bucket, key } = CoreStorageModule.extractBucketAndKey(fileUrl);

      const validationResult = ParentFolderE.safeParse(bucket);
      if (!validationResult.success) {
        throw new AppError('STRG005');
      }

      const command = new DeleteObjectCommand({
        Bucket: bucket,
        Key: key,
      });

      await s3.send(command);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('STRG004');
    }
  },
};
