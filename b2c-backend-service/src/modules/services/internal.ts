import AppError from '@classes/AppError';
import { prismaMG } from '@config/db';
import type { GetInternalServiceResultI, InternalServiceI } from '@interfaces/services';
import { InternalServiceNameE, Prisma } from '@prisma/mongodb';
import { decryptJSON } from '@utils/cryptography/encDec';
import { isFilled } from '@utils/data/object';
import { ObjUnknownI } from '@interfaces/common/data';
import { isFilledString } from '@utils/data/string';
import CommunicationService, { CommunicationServiceConfigI } from '@navicater/b2c-internal-communication';

const InternalServiceModule = {
  fetchByName: async (filters: Pick<Prisma.InternalServiceWhereUniqueInput, 'name'>): Promise<InternalServiceI> => {
    const select: Prisma.InternalServiceSelect = {
      name: true,
      config: true,
      credsConfig: true,
    };
    const tempResult = await prismaMG.internalService.findFirst({
      where: filters,
      select,
    });
    if (!tempResult) {
      throw new AppError('VND001');
    }
    let config: ObjUnknownI = {};
    if (isFilled(tempResult.config)) {
      config = {
        ...(tempResult.config as ObjUnknownI),
      };
    }
    if (isFilledString(tempResult?.credsConfig)) {
      config = { ...config, ...(decryptJSON(tempResult.credsConfig) as ObjUnknownI) };
    }
    const internalServiceResult: InternalServiceI = {
      ...tempResult,
      config,
    };
    return internalServiceResult;
  },
  getVendor: async (name: InternalServiceNameE): Promise<GetInternalServiceResultI> => {
    const internalServiceResult: InternalServiceI = await InternalServiceModule.fetchByName({ name });
    const config: ObjUnknownI = internalServiceResult.config as ObjUnknownI;

    switch (name) {
      case 'COMMUNICATION': {
        return {
          name,
          config: config as CommunicationServiceConfigI,
          instance: new CommunicationService(config as CommunicationServiceConfigI),
        };
      }

      default: {
        throw new AppError('INSVC001');
      }
    }
  },
};

export default InternalServiceModule;
