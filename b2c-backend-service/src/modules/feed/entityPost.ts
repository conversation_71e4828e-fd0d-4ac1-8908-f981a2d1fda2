import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { EntityPostDataClientI, EntityPostFetchManyResultI } from '@interfaces/feed/entityPost';
import { Post, Prisma } from '@prisma/postgres';
import { EntityPostCreateOneI, EntityPostFetchManyI } from '@schemas/feed/entityPost';
import { errorHandler } from '@utils/errors/handler';
import { omit } from '@utils/data/object';

const EntityPostModule = {
  createOne: async (
  state: FastifyStateI,
  { caption, files, entityId, entityRawDataId }: EntityPostCreateOneI
): Promise<Omit<Post, 'cursorId'> & { cursorId?: number }>  => {
  try {
  const selfProfileId =state.profileId;
  console.log('Creating post for:', state.profileId, entityId, entityRawDataId);
    const membership = await prismaPG.entityMember.findFirst({
      where: {
        profileId:selfProfileId,
        OR: [
          { entityId },
          { entityRawDataId }
        ],
        role: {
          in: ['ADMIN', 'MAINTAINER']
        }
      },
      include: {
        Profile: {
          select: {
            status: true
          }
        }
      }
    });

    if (!membership) {
      throw new AppError('ENT007');
    }


    if (membership.Profile.status !== 'ACTIVE') {
      throw new AppError('PFL015');
    }

    const post = await prismaPG.post.create({
      data: {
        caption,
        profileId:selfProfileId,
        entityId,
        entityRawDataId,
        status: 'ACTIVE',
        Media: files?.length ? {
          createMany: {
            data: files.map(file => ({
              fileUrl: file.fileUrl,
              fileExtension: file.extension,
              caption: file.caption,
              profileId:selfProfileId
            }))
          }
        } : undefined
      },
      include: { Media: true }
    });

return {
  ...post,
  cursorId: post.cursorId ? Number(post.cursorId) : undefined,

};


  } catch (error) {
    console.error('Create post error:', error);
    errorHandler(error);
  }
},
  fetchMany: async (
    { entityId, entityRawDataId, ...pagination }: EntityPostFetchManyI,
    profileId?: string
  ): Promise<EntityPostFetchManyResultI> => {
    try {
      const result: EntityPostFetchManyResultI = { posts: [] };
      const cursorId = pagination.cursorId ? Number(pagination.cursorId) : undefined;

      const posts: EntityPostDataClientI[] = await prismaPG.$queryRaw`
        SELECT
          p."id",
          p."caption",
          CASE
            WHEN length(p."caption") > 100 THEN LEFT(p."caption", 100) || '...'
            ELSE p."caption"
          END AS "shortCaption",
          length(p."caption") > 100 AS "isCaptionTruncated",
          p."cursorId",
          p."reactionsCount",
          p."totalCommentsCount",
          EXISTS (
            SELECT 1 FROM "feed"."PostReaction" pr
            WHERE pr."postId" = p."id"
            AND pr."profileId" = ${profileId}::uuid
          ) AS "isLiked",
        json_build_object(
          'id', COALESCE(e."id", erd."id"),
          'name', COALESCE(e."name", erd."name"),
          'type', CASE WHEN p."entityId" IS NOT NULL THEN 'master' ELSE 'raw' END
        ) AS "entity",
        CASE
          WHEN p."entityId" IS NOT NULL THEN
            json_build_object(
              'id', e."id",
              'name', e."name",
              'avatar', (SELECT "avatar" FROM "user"."Profile" WHERE "entityId" = e."id" LIMIT 1)
            )
          ELSE
            json_build_object(
              'id', prof."id",
              'name', prof."name",
              'avatar', prof."avatar"
            )
        END AS "Profile",
                (
            SELECT json_agg(json_build_object(
              'caption', pm."caption",
              'fileUrl', pm."fileUrl"
            ))
            FROM "feed"."PostMedia" pm
            WHERE pm."postId" = p."id"
          ) AS "Media"
        FROM "feed"."Post" p
        LEFT JOIN "company"."Entity" e ON p."entityId" = e."id"
        LEFT JOIN "rawData"."EntityRawData" erd ON p."entityRawDataId" = erd."id"
        INNER JOIN "user"."Profile" prof ON p."profileId" = prof."id"
        WHERE p."status" = 'ACTIVE'
        AND (
  ${entityId || entityRawDataId
    ? Prisma.sql`(p."entityId" = ${entityId}::uuid OR p."entityRawDataId" = ${entityRawDataId}::uuid)`
    : Prisma.sql`(p."entityId" IS NOT NULL OR p."entityRawDataId" IS NOT NULL)`
  }
)
        ${cursorId ? Prisma.sql`AND p."cursorId" < ${cursorId}` : Prisma.empty}
        ORDER BY p."createdAt" DESC
        LIMIT ${pagination.pageSize}
      `;

      if (posts.length) {
        result.posts = posts.map(post => ({
          ...post,
          cursorId: Number(post.cursorId),
        }));
        result.cursorId = Number(posts[posts.length - 1].cursorId);
      }

      return result;
    } catch (error) {
      errorHandler(error);
    }
  },
};

export default EntityPostModule;
