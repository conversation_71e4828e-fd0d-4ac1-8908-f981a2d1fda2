import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import type { FastifyStateI } from '@interfaces/common/declaration';
import type { ScrapBookPostCreateOneResultI, ScrapBookPostFetchForClientI } from '@interfaces/port/post';
import { Prisma } from '@prisma/postgres';
import type { PaginationI } from '@schemas/common/common';
import type { ScrapBookPostCreateOneParamsI, ScrapBookPostFetchForClientParamsI } from '@schemas/port/post';
import RewardModule from '@modules/reward';
import { REWARD_TYPE_IDS } from '@consts/reward/reward';

export const ScrapBookPostModule = {
  fetchOne: async (
    state: FastifyStateI,
    filtersP: Pick<Prisma.ScrapBookPostWhereUniqueInput, 'id'>,
  ): Promise<ScrapBookPostFetchForClientI> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.ScrapBookPostWhereUniqueInput = { ...filtersP, status: 'ACTIVE' };
    const scrapBookPostResult = await prismaPG.scrapBookPost.findUnique({
      where: filters,
      select: {
        id: true,
        commentCount: true,
        reactionCount: true,
        textPreview: true,
        text: true,
        createdAt: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
          },
        },
        ScrapBookReaction: {
          where: {
            profileId: selfProfileId,
          },
          select: {
            reactionType: true,
          },
        },
      },
    });

    if (!scrapBookPostResult) {
      throw new AppError('SCBKPST001');
    }

    const originalTextPreview = scrapBookPostResult.textPreview || '';
    const isCaptionTruncated = scrapBookPostResult.text.length > originalTextPreview.length;

    const scrapBookPostFetchForClientResult: ScrapBookPostFetchForClientI = {
      id: scrapBookPostResult.id,
      text: scrapBookPostResult.text,
      commentCount: scrapBookPostResult.commentCount,
      reactionCount: scrapBookPostResult.reactionCount,
      textPreview: originalTextPreview,
      isCaptionTruncated: isCaptionTruncated,
      createdAt: scrapBookPostResult.createdAt,
      profile: scrapBookPostResult.Profile,
      isLiked: scrapBookPostResult.ScrapBookReaction.length > 0,
    };

    return scrapBookPostFetchForClientResult;
  },
  fetchForClient: async (
    state: FastifyStateI,
    params: ScrapBookPostFetchForClientParamsI,
    pagination: PaginationI = PAGINATION,
  ): Promise<{ data: ScrapBookPostFetchForClientI[]; total: number }> => {
    const selfProfileId = state.profileId;
    const filters: Prisma.ScrapBookPostWhereInput = {
      status: 'ACTIVE',
      portUnLocode: params.portUnLocode,
    };

    const total = await prismaPG.scrapBookPost.count({
      where: filters,
    });

    const skip = pagination.page > 0 ? (pagination.page - 1) * pagination.pageSize : 0;

    const scrapBookPostResult = await prismaPG.scrapBookPost.findMany({
      where: filters,
      select: {
        id: true,
        commentCount: true,
        reactionCount: true,
        textPreview: true,
        text: true,
        createdAt: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
          },
        },
        ScrapBookReaction: {
          where: {
            profileId: selfProfileId,
          },
          select: {
            reactionType: true,
          },
        },
      },
      skip: skip,
      take: pagination.pageSize,
      orderBy: {
        createdAt: 'desc',
      },
    });

    const scrapBookPostFetchForClientResult: ScrapBookPostFetchForClientI[] = scrapBookPostResult.map((post) => {
      const isCaptionTruncated = post.text.length > post.textPreview.length;

      return {
        id: post.id,
        text: post.text,
        commentCount: post.commentCount,
        reactionCount: post.reactionCount,
        textPreview: post.textPreview,
        isCaptionTruncated: isCaptionTruncated,
        createdAt: post.createdAt,
        profile: post.Profile,
        isLiked: post.ScrapBookReaction.length > 0,
      };
    });

    return { data: scrapBookPostFetchForClientResult, total };
  },
  createOne: async (
    state: FastifyStateI,
    params: ScrapBookPostCreateOneParamsI,
  ): Promise<ScrapBookPostCreateOneResultI> => {
    if (!params.text || params.text.trim().length === 0) {
      throw new AppError('SCBKPST001');
    }

    const input: Prisma.ScrapBookPostUncheckedCreateInput = {
      profileId: state.profileId,
      text: params.text.trim(),
      textPreview: params.text.trim().slice(0, 120),
      portUnLocode: params.portUnLocode,
      status: 'ACTIVE',
    };

    const scrapBookPostResult = await prismaPG.scrapBookPost.create({
      data: input,
      select: {
        id: true,
      },
    });

    try {
      await RewardModule.RewardActionModule.assignReward({
        profileId: state.profileId,
        rewardId: REWARD_TYPE_IDS.CONTRIBUTION,
      });
    } catch (rewardError) {
      console.error('Failed to assign reward for scrapbook post creation:', rewardError);
    }

    return scrapBookPostResult;
  },
  updateOne: async (
    state: FastifyStateI,
    filtersP: Pick<Prisma.ScrapBookPostWhereUniqueInput, 'id'>,
    params: ScrapBookPostCreateOneParamsI,
  ): Promise<ScrapBookPostCreateOneResultI> => {
    if (!params.text || params.text.trim().length === 0) {
      throw new AppError('SCBKPST001');
    }

    const existingPostResult = await prismaPG.scrapBookPost.findUnique({
      select: {
        id: true,
        profileId: true,
      },
      where: {
        id: filtersP.id,
        status: 'ACTIVE',
      },
    });

    if (!existingPostResult) {
      throw new AppError('SCBKPST001');
    }

    if (existingPostResult.profileId !== state.profileId) {
      throw new AppError('SCBKPST001');
    }

    const input: Prisma.ScrapBookPostUncheckedUpdateInput = {
      text: params.text.trim(),
      textPreview: params.text.trim().slice(0, 120),
      updatedAt: new Date(),
    };

    const scrapBookPostResult = await prismaPG.scrapBookPost.update({
      where: { id: filtersP.id },
      data: input,
      select: {
        id: true,
      },
    });

    return scrapBookPostResult;
  },
  deleteOne: async (
    state: FastifyStateI,
    filtersP: Pick<Prisma.ScrapBookPostWhereUniqueInput, 'id'>,
  ): Promise<void> => {
    const existingPostResult = await prismaPG.scrapBookPost.findUnique({
      select: {
        id: true,
        profileId: true,
      },
      where: {
        id: filtersP.id,
        status: 'ACTIVE',
      },
    });

    if (!existingPostResult) {
      throw new AppError('SCBKPST001');
    }

    if (existingPostResult.profileId !== state.profileId) {
      throw new AppError('SCBKPST001');
    }

    await prismaPG.$transaction(async (tx) => {
      await tx.scrapBookComment.deleteMany({
        where: {
          scrapBookPostId: existingPostResult.id,
        },
      });

      await tx.scrapBookReaction.deleteMany({
        where: {
          scrapBookPostId: existingPostResult.id,
        },
      });

      await tx.scrapBookPost.delete({
        where: { id: existingPostResult.id },
      });
    });
  },
  fetchFullTextPreview: async (
    filtersP: Pick<Prisma.ScrapBookPostWhereUniqueInput, 'id'>,
  ): Promise<{ caption: string }> => {
    const filters: Prisma.ScrapBookPostWhereUniqueInput = { ...filtersP, status: 'ACTIVE' };
    const scrapBookPostResult = await prismaPG.scrapBookPost.findUnique({
      where: filters,
      select: {
        text: true,
      },
    });

    if (!scrapBookPostResult) {
      throw new AppError('SCBKPST001');
    }

    return {
      caption: scrapBookPostResult.text || '',
    };
  },
};
