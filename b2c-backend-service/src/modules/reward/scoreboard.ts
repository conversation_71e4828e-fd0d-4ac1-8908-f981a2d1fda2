import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI, RewardProfileI } from '@interfaces/reward/scoreboard';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

type MaxScoresI = {
  maxQnaAnswerScore: number | null;
  maxTroubleshootAnswerScore: number | null;
  maxContributionScore: number | null;
  maxTotalScore: number | null;
};

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI): Promise<ScoreboardFetchForClientResultI> => {
    const profileId = request.profileId;

    if (!profileId) {
      throw new AppError('AUTH019');
    }

    const userResult = await prismaPG.$queryRaw<RewardProfileI[]>(
      Prisma.sql`
        SELECT * FROM "score"."RewardProfile"
        WHERE "profileId" = ${profileId}::uuid
      `,
    );

    if (!userResult || userResult.length === 0) {
      throw new AppError('AUTH019');
    }

    const userProfile = userResult[0];

    const maxScoresResult = await prismaPG.$queryRaw<MaxScoresI[]>(
      Prisma.sql`
        SELECT
          "maxQnaAnswerScore",
          "maxTroubleshootAnswerScore",
          "maxContributionScore",
          "maxTotalScore"
        FROM "leaderboard"."MaxScores"
        LIMIT 1
      `,
    );

    const maxScores = maxScoresResult[0];

    const calculatePerformanceIndex = (userScore: number, maxScore: number | null): number => {
      if (!maxScore || maxScore === 0) return 0;
      return Math.round((userScore / maxScore) * 100);
    };

    const result = {
      ...userProfile,
      qnaPerformanceIndex: calculatePerformanceIndex(userProfile.qnaAnswerScore, maxScores.maxQnaAnswerScore),
      troubleshootPerformanceIndex: calculatePerformanceIndex(
        userProfile.troubleshootAnswerScore,
        maxScores.maxTroubleshootAnswerScore,
      ),
      contributionPerformanceIndex: calculatePerformanceIndex(
        userProfile.contributionScore,
        maxScores.maxContributionScore,
      ),
    };

    return result;
  },
};
