import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { ScoreboardFetchForClientResultI, RewardProfileI } from '@interfaces/reward/scoreboard';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import { Prisma } from '@prisma/postgres';

type MaxScoresI = {
  maxQnaAnswerScore: number | null;
  maxTroubleshootAnswerScore: number | null;
  maxContributionScore: number | null;
};

export const ScoreboardModule = {
  fetch: async (request: FastifyRequestI): Promise<ScoreboardFetchForClientResultI> => {
    const profileId = request.profileId;

    if (!profileId) {
      throw new AppError('AUTH019');
    }

    // Get user's reward profile
    const userResult = await prismaPG.$queryRaw<RewardProfileI[]>(
      Prisma.sql`
        SELECT * FROM "score"."RewardProfile"
        WHERE "profileId" = ${profileId}::uuid
      `,
    );

    if (!userResult || userResult.length === 0) {
      throw new AppError('AUTH019');
    }

    const userProfile = userResult[0];

    // Get highest scores for each category
    const maxScoresResult = await prismaPG.$queryRaw<MaxScoresI[]>(
      Prisma.sql`
        SELECT
          MAX("qnaAnswerScore") as "maxQnaAnswerScore",
          MAX("troubleshootAnswerScore") as "maxTroubleshootAnswerScore",
          MAX("contributionScore") as "maxContributionScore"
        FROM "score"."RewardProfile"
      `,
    );

    const maxScores = maxScoresResult[0];

    // Calculate performance indices: (user score / highest score) * 100
    const calculatePerformanceIndex = (userScore: number, maxScore: number | null): number => {
      if (!maxScore || maxScore === 0) return 0;
      return Math.round((userScore / maxScore) * 100);
    };

    // Add performance indices to the result
    const result = {
      ...userProfile,
      qnaPerformanceIndex: calculatePerformanceIndex(userProfile.qnaAnswerScore, maxScores.maxQnaAnswerScore),
      troubleshootPerformanceIndex: calculatePerformanceIndex(
        userProfile.troubleshootAnswerScore,
        maxScores.maxTroubleshootAnswerScore,
      ),
      contributionPerformanceIndex: calculatePerformanceIndex(
        userProfile.contributionScore,
        maxScores.maxContributionScore,
      ),
    };

    return result;
  },
};
