import appleSigninAuth, { AppleIdTokenType } from 'apple-signin-auth';
import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import type { AuthLoginParamsI, AuthRegisterParamsI } from '@schemas/auth/auth';
import { comparePassword, hashPassword } from '@utils/cryptography/password';
import SessionModule from './session';
import { Prisma } from '@prisma/postgres';
import type { Profile, ProfileMeta, ProfileStatus, Session } from '@prisma/postgres';
import User from '@modules/user';
import { RequireSome } from '@interfaces/common/data';
import type {
  AuthLoginResultI,
  AuthRegisterResultI,
  ResetPasswordI,
  SendOTPForPasswordResetI,
  VerifyOTPForPasswordResetI,
} from '@interfaces/auth/auth';
import AppModule from '@modules/app';
import Google from '@navicater/vendor-google';
import type { ProfileUpdateOneDataI } from '@interfaces/user/profile';
import jwt from 'jsonwebtoken';
import CommunicationModule from '@modules/communication';
import { ENV } from '@consts/common/env';
import ServiceModule from '@modules/services';
import type { VerifyOTPForEmailVerificationI } from '@schemas/user/profile';
import { addMsToDate, getCurrentDate } from '@utils/data/date';
import AppConfig from '@modules/appConfig';
import type { CommunicationConfigI } from '@interfaces/appConfig/appConfig';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { ProfileStatusI } from '@consts/user/profile';
import { TokenPayload } from 'google-auth-library';
import { ReferralModule } from '@modules/referral';

const AuthModule = {
  register: async (params: AuthRegisterParamsI): Promise<AuthRegisterResultI> => {
    const profileSelect: Prisma.ProfileSelect = {
      id: true,
      email: true,
    };
    if (params.type !== 'EMAIL_PASSWORD') {
      throw new AppError('AUTH008');
    }
    const existingProfile: Pick<Profile, 'id'> = await prismaPG.profile.findFirst({
      where: {
        email: params.email!,
        status: {
          notIn: ['DELETED'],
        },
      },
      select: profileSelect,
    });
    if (existingProfile) {
      throw new AppError('AUTH005');
    }
    const encryptedPassword = await hashPassword(params.password);

    let appVersionResult: Awaited<ReturnType<typeof AppModule.AppVersionModule.fetchByIdVersionPlatform>> | undefined;
    if (params.platform !== 'web_app' && params.versionNo) {
      appVersionResult = await AppModule.AppVersionModule.fetchByIdVersionPlatform({
        versionNo: params.versionNo,
        platformId: params.platform,
      });

      if (appVersionResult.platformId !== params.platform) {
        throw new AppError('AUTH009');
      }
    }

    const [profile, _profileStatus, _profileMeta, sessionResult]: [
      Pick<Profile, 'id' | 'email'>,
      Pick<ProfileStatus, 'profileId'>,
      Pick<ProfileMeta, 'profileId'>,
      Pick<Session, 'id'>,
    ] = await prismaPG.$transaction(async (txn) => {
      const profileTemp = await txn.profile.create({
        data: {
          email: params.email,
          password: encryptedPassword,
        },
        select: profileSelect,
      });

      const [profileStatusTemp, profileMetaTemp, sessionTemp] = await Promise.all([
        txn.profileStatus.create({
          data: {
            profileId: profileTemp.id,
            isPasswordSaved: true,
            isPrivacyPolicyAccepted: params.isPPAndTNCAccepted,
          } as Prisma.ProfileStatusUncheckedCreateInput,
          select: {
            profileId: true,
          },
        }),
        txn.profileMeta.create({
          data: { profileId: profileTemp.id },
          select: { profileId: true },
        }),
        SessionModule.createOne(txn, {
          ipAddress: params.ip,
          profileId: profileTemp.id,
          appVersionId: appVersionResult?.id, // Use optional chaining for WEB_APP case
          deviceId: params.deviceId,
          platformId: params.platform,
        }),
      ]);

      return [profileTemp, profileStatusTemp, profileMetaTemp, sessionTemp];
    });

    const authResult: AuthRegisterResultI = {
      profileId: profile.id,
      token: sessionResult.id,
    };
    return authResult as AuthRegisterResultI;
  },
  sendOTPForEmailVerification: async ({ profileId: selfProfileId }: FastifyStateI): Promise<void> => {
    const profileResult = await prismaPG.profile.findUnique({
      select: {
        email: true,
        name: true,
        googleSub: true,
        ProfileStatus: {
          select: {
            isEmailVerified: true,
          },
        },
        ProfileMeta: {
          select: {
            emailVerificationCount: true,
            emailVerificationFirstAttemptAt: true,
          },
        },
      },
      where: {
        id: selfProfileId,
      },
    });
    if ([profileResult?.ProfileStatus?.isEmailVerified, profileResult?.googleSub].includes(true)) {
      throw new AppError('AUTH024');
    }
    const appConfigCommunicationResult: CommunicationConfigI = (await AppConfig.AppConfigModule.fetchByIdWithFallback(
      {
        module: 'COMMUNICATION',
        subModule: 'COMMUNICATION',
      },
      undefined,
      'web_app',
    )) as CommunicationConfigI;
    const limitConfig = appConfigCommunicationResult.verification.email.limit;
    const currentDate = getCurrentDate();

    const toUpdateProfileMeta: Prisma.ProfileMetaUncheckedUpdateInput = {};

    if (profileResult?.ProfileMeta.emailVerificationCount < limitConfig.count) {
      if (profileResult?.ProfileMeta?.emailVerificationCount === 0) {
        toUpdateProfileMeta.emailVerificationFirstAttemptAt = currentDate;
      }
      toUpdateProfileMeta.emailVerificationCount = {
        increment: 1,
      };
    } else {
      const attemptExpiryDate = addMsToDate({
        date: profileResult.ProfileMeta.emailVerificationFirstAttemptAt,
        ms: appConfigCommunicationResult.verification.email.limit.duration,
      });
      if (currentDate < attemptExpiryDate) {
        throw new AppError('AUTH026');
      } else {
        toUpdateProfileMeta.emailVerificationFirstAttemptAt = currentDate;
        toUpdateProfileMeta.emailVerificationCount = 0;
      }
    }
    try {
      await CommunicationModule.EmailModule.sendOne({
        email: profileResult.email,
        name: profileResult.name,
        profileId: selfProfileId,
        type: 'EMAIL_ID_VERIFICATION',
      });
    } catch (_error) {
      throw new AppError('AUTH027');
    }
    await prismaPG.profileMeta.update({
      data: toUpdateProfileMeta,
      select: { profileId: true },
      where: {
        profileId: selfProfileId,
      },
    });
    return;
  },
  verifyOTPForEmailVerification: async (
    { profileId: selfProfileId }: FastifyStateI,
    { otp }: VerifyOTPForEmailVerificationI,
  ): Promise<void> => {
    const profileResult = await prismaPG.profile.findUnique({
      select: {
        name: true,
        googleSub: true,
        ProfileStatus: {
          select: {
            isEmailVerified: true,
          },
        },
      },
      where: {
        id: selfProfileId,
      },
    });
    if ([profileResult?.ProfileStatus?.isEmailVerified, profileResult?.googleSub].includes(true)) {
      throw new AppError('AUTH024');
    }
    const _verifyResult = await CommunicationModule.EmailModule.verify({
      otp,
      profileId: selfProfileId,
      type: 'EMAIL_ID_VERIFICATION',
    });
    await prismaPG.profileStatus.update({ data: { isEmailVerified: true }, where: { profileId: selfProfileId } });
    return;
  },

  sendOTPForPasswordReset: async ({ email }: SendOTPForPasswordResetI): Promise<void> => {
    const profileResult = await prismaPG.profile.findFirst({
      select: {
        id: true,
        email: true,
        name: true,
        googleSub: true,
        ProfileStatus: {
          select: {
            isPasswordSaved: true,
          },
        },
        ProfileMeta: {
          select: {
            passwordResetCount: true,
            passwordResetFirstAttemptAt: true,
          },
        },
      },
      where: {
        email: email,
        status: {
          notIn: ['DELETED'],
        },
      },
    });

    if (!profileResult) {
      throw new AppError('AUTH002');
    }

    if (profileResult.googleSub && !profileResult.ProfileStatus?.isPasswordSaved) {
      throw new AppError('AUTH023');
    }

    const appConfigCommunicationResult: CommunicationConfigI = (await AppConfig.AppConfigModule.fetchByIdWithFallback(
      {
        module: 'COMMUNICATION',
        subModule: 'COMMUNICATION',
      },
      undefined,
      'web_app',
    )) as CommunicationConfigI;

    const limitConfig = appConfigCommunicationResult.verification.email.limit;
    const currentDate = getCurrentDate();

    const toUpdateProfileMeta: Prisma.ProfileMetaUncheckedUpdateInput = {};

    if ((profileResult?.ProfileMeta?.passwordResetCount || 0) < limitConfig.count) {
      if ((profileResult?.ProfileMeta?.passwordResetCount || 0) === 0) {
        toUpdateProfileMeta.passwordResetFirstAttemptAt = currentDate;
      }
      toUpdateProfileMeta.passwordResetCount = {
        increment: 1,
      };
    } else {
      const attemptExpiryDate = addMsToDate({
        date: profileResult.ProfileMeta.passwordResetFirstAttemptAt,
        ms: limitConfig.duration,
      });
      if (currentDate < attemptExpiryDate) {
        throw new AppError('AUTH026');
      } else {
        toUpdateProfileMeta.passwordResetFirstAttemptAt = currentDate;
        toUpdateProfileMeta.passwordResetCount = 1;
      }
    }

    try {
      await CommunicationModule.EmailModule.sendOne({
        email: profileResult.email,
        name: profileResult.name,
        profileId: profileResult.id,
        type: 'EMAIL_ID_PASSWORD_RESET',
      });
    } catch (_error) {
      throw new AppError('AUTH027');
    }

    await prismaPG.profileMeta.update({
      data: toUpdateProfileMeta,
      select: { profileId: true },
      where: {
        profileId: profileResult.id,
      },
    });

    return;
  },
  verifyOTPForPasswordReset: async ({ email, otp }: VerifyOTPForPasswordResetI): Promise<void> => {
    const profileResult = await prismaPG.profile.findFirst({
      select: {
        id: true,
        googleSub: true,
        ProfileStatus: {
          select: {
            isPasswordSaved: true,
          },
        },
      },
      where: {
        email: email,
        status: {
          notIn: ['DELETED'],
        },
      },
    });

    if (!profileResult) {
      throw new AppError('AUTH002');
    }

    if (profileResult.googleSub && !profileResult.ProfileStatus?.isPasswordSaved) {
      throw new AppError('AUTH023');
    }

    await CommunicationModule.EmailModule.verify({
      otp,
      profileId: profileResult.id,
      type: 'EMAIL_ID_PASSWORD_RESET',
    });

    return;
  },

  resetPassword: async ({ email, newPassword }: ResetPasswordI): Promise<void> => {
    const profileResult = await prismaPG.profile.findFirst({
      select: {
        id: true,
        googleSub: true,
        ProfileStatus: {
          select: {
            isPasswordSaved: true,
          },
        },
      },
      where: {
        email: email,
        status: {
          notIn: ['DELETED'],
        },
      },
    });

    if (!profileResult) {
      throw new AppError('AUTH002');
    }

    if (profileResult.googleSub && !profileResult.ProfileStatus?.isPasswordSaved) {
      throw new AppError('AUTH023');
    }

    const encryptedPassword = await hashPassword(newPassword);

    await prismaPG.$transaction(async (txn) => {
      await txn.profile.update({
        data: {
          password: encryptedPassword,
        },
        where: {
          id: profileResult.id,
        },
      });

      await txn.profileStatus.update({
        data: {
          isPasswordSaved: true,
        },
        where: {
          profileId: profileResult.id,
        },
      });

      await txn.session.updateMany({
        data: {
          isActive: false,
        },
        where: {
          profileId: profileResult.id,
          isActive: true,
        },
      });
    });

    const existingSessionResult = await prismaPG.session.findFirst({
      select: {
        id: true,
      },
      where: {
        profileId: profileResult.id,
        isActive: true,
      },
    });

    if (existingSessionResult) {
      await CommunicationModule.SessionModule.deleteOne({
        opr: 'DELETE',
        sessionId: existingSessionResult.id,
      });
    }
    return;
  },
  login: async (params: AuthLoginParamsI): Promise<AuthLoginResultI> => {
    const profileSelect: Prisma.ProfileSelect = {
      id: true,
      email: true,
      username: true,
      googleSub: true,
      appleSub: true,
      avatar: true,
      status: true,
      name: true,
      designationText: true,
      entityText: true,
    };

    const profileStatusSelect: Prisma.ProfileStatusSelect = {
      isPasswordSaved: true,
      isEmailVerified: true,
      isPersonalDetailsSaved: true,
      isWorkDetailsSaved: true,
      isPrivacyPolicyAccepted: true,
    };

    let profile: RequireSome<Profile, 'id' | 'email' | 'username' | 'status'>;
    let profileStatus: Pick<
      ProfileStatus,
      | 'isPasswordSaved'
      | 'isPersonalDetailsSaved'
      | 'isWorkDetailsSaved'
      | 'isEmailVerified'
      | 'isPrivacyPolicyAccepted'
    >;
    let previousStatus: ProfileStatusI;
    let _profileMeta: Pick<ProfileMeta, 'profileId'>;
    let existingSessionResult: Pick<Session, 'id'>;

    if (params.type === 'GOOGLE') {
      const { instance } = await ServiceModule.VendorModule.getVendor('GOOGLE');
      let payloadResult: TokenPayload;

      try {
        payloadResult = await (instance as Google).verifyGoogleIdToken(params.externalToken);
      } catch (_error) {
        throw new AppError('AUTH010');
      }

      profile = await prismaPG.profile.findFirst({
        where: {
          email: payloadResult.email!,
          status: {
            notIn: ['DELETED'],
          },
        },
        select: profileSelect,
      });

      if (profile) {
        previousStatus = profile?.status;
      }

      if (!profile) {
        [profile, profileStatus, _profileMeta] = await prismaPG.$transaction(async (txn) => {
          const profileTemp = await txn.profile.create({
            data: {
              email: payloadResult.email,
              googleSub: payloadResult.sub,
            },
            select: profileSelect,
          });

          previousStatus = profileTemp.status;

          const profileStatusTemp = await txn.profileStatus.create({
            data: { profileId: profileTemp.id, isEmailVerified: true } as Prisma.ProfileStatusUncheckedCreateInput,
            select: profileStatusSelect,
          });

          const profileMetaTemp = await txn.profileMeta.create({
            data: { profileId: profileTemp.id },
            select: { profileId: true },
          });

          return [profileTemp, profileStatusTemp, profileMetaTemp];
        });
      } else {
        if (profile.appleSub && !profile.googleSub) {
          throw new AppError('AUTH021', 'Email used with Apple login');
        }

        if (!profile?.googleSub?.length) {
          const toUpdateProfileParams: ProfileUpdateOneDataI = { googleSub: payloadResult.sub };
          if (!profile?.avatar?.length) {
            toUpdateProfileParams.avatar = payloadResult.picture;
          }
          profile = await User.ProfileModule.updateOne(toUpdateProfileParams, { id: profile.id }, profileSelect);
        }

        if (profile.status === 'INACTIVE' || profile.status === 'SCHEDULED_FOR_DELETION') {
          const toUpdateProfileParams: ProfileUpdateOneDataI = { status: 'ACTIVE' };
          profile = await User.ProfileModule.updateOne(toUpdateProfileParams, { id: profile.id }, profileSelect);
          await User.ProfileStatusModule.updateOne({ deletionInitiatedAt: null }, { profileId: profile.id });
        }

        if (profile.googleSub !== payloadResult.sub) {
          throw new AppError('AUTH004');
        }

        [profileStatus, existingSessionResult] = await Promise.all([
          User.ProfileStatusModule.fetchById({ profileId: profile.id }, profileStatusSelect),
          prismaPG.session.findFirst({
            select: {
              id: true,
            },
            where: {
              profileId: profile.id,
              platformId: params.platform,
              isActive: true,
            },
          }),
        ]);

        if (!profileStatus.isEmailVerified) {
          profileStatus = await User.ProfileStatusModule.updateOne(
            { isEmailVerified: true },
            { profileId: profile.id },
          );
        }
      }
    } else if (params.type === 'APPLE') {
      let payloadResult: AppleIdTokenType;

      try {
        payloadResult = await appleSigninAuth.verifyIdToken(params.externalToken, {
          audience: 'com.navicater',
        });
      } catch (_error) {
        throw new AppError('AUTH010');
      }

      const userEmail = params.email;

      if (userEmail && userEmail.trim() !== '') {
        profile = await prismaPG.profile.findFirst({
          where: {
            OR: [{ appleSub: payloadResult.sub }, { email: userEmail }],
          },
          select: profileSelect,
        });
      } else {
        profile = await prismaPG.profile.findFirst({
          where: {
            appleSub: payloadResult.sub,
          },
          select: profileSelect,
        });
      }

      if (profile) {
        previousStatus = profile.status;
      }

      if (!profile) {
        const emailToUse = userEmail && userEmail.trim() !== '' ? userEmail : null;

        [profile, profileStatus, _profileMeta] = await prismaPG.$transaction(async (txn) => {
          const profileTemp = await txn.profile.create({
            data: {
              email: emailToUse,
              appleSub: payloadResult.sub,
            },
            select: profileSelect,
          });

          previousStatus = profileTemp.status;

          const profileStatusTemp = await txn.profileStatus.create({
            data: { profileId: profileTemp.id, isEmailVerified: true } as Prisma.ProfileStatusUncheckedCreateInput,
            select: profileStatusSelect,
          });

          const profileMetaTemp = await txn.profileMeta.create({
            data: { profileId: profileTemp.id },
            select: { profileId: true },
          });

          return [profileTemp, profileStatusTemp, profileMetaTemp];
        });
      } else {
        if (profile.googleSub && !profile.appleSub) {
          throw new AppError('AUTH020', 'Email used with Google login');
        }

        const updateParams: ProfileUpdateOneDataI = {};
        let shouldUpdate = false;

        if (!profile?.appleSub?.length) {
          updateParams.appleSub = payloadResult.sub;
          shouldUpdate = true;
        }

        if (userEmail && userEmail.trim() !== '' && (!profile.email || profile.email.includes('@navicater.temp'))) {
          updateParams.email = userEmail;
          shouldUpdate = true;
        }

        if (shouldUpdate) {
          profile = await User.ProfileModule.updateOne(updateParams, { id: profile.id }, profileSelect);
        }

        if (profile.appleSub && profile.appleSub !== payloadResult.sub) {
          throw new AppError('AUTH004');
        }

        if (profile.status === 'INACTIVE' || profile.status === 'SCHEDULED_FOR_DELETION') {
          const toUpdateProfileParams: ProfileUpdateOneDataI = { status: 'ACTIVE' };
          profile = await User.ProfileModule.updateOne(toUpdateProfileParams, { id: profile.id }, profileSelect);
          await User.ProfileStatusModule.updateOne({ deletionInitiatedAt: null }, { profileId: profile.id });
        }

        [profileStatus, existingSessionResult] = await Promise.all([
          User.ProfileStatusModule.fetchById({ profileId: profile.id }, profileStatusSelect),
          prismaPG.session.findFirst({
            select: {
              id: true,
            },
            where: {
              profileId: profile.id,
              platformId: params.platform,
              isActive: true,
            },
          }),
        ]);

        if (!profileStatus.isEmailVerified) {
          profileStatus = await User.ProfileStatusModule.updateOne(
            { isEmailVerified: true },
            { profileId: profile.id },
          );
        }
      }
    } else {
      profile = await prismaPG.profile.findFirst({
        where: {
          email: params.email!,
          status: {
            notIn: ['DELETED'],
          },
        },
        select: {
          ...profileSelect,
          password: true,
        },
      });

      if (!profile) {
        throw new AppError('AUTH002');
      }

      previousStatus = profile.status;

      existingSessionResult = await prismaPG.session.findFirst({
        select: {
          id: true,
        },
        where: {
          profileId: profile.id,
          platformId: params.platform,
          isActive: true,
        },
      });

      profileStatus = await User.ProfileStatusModule.fetchById({ profileId: profile.id! }, profileStatusSelect);

      if (!profileStatus.isPasswordSaved) {
        if (profile?.googleSub) {
          throw new AppError('AUTH023');
        }
        throw new AppError('AUTH003');
      }

      const isEqual = await comparePassword(profile.password, params.password);
      if (!isEqual) {
        throw new AppError('AUTH001');
      }

      if (profile.status === 'INACTIVE' || profile.status === 'SCHEDULED_FOR_DELETION') {
        const toUpdateProfileParams: ProfileUpdateOneDataI = { status: 'ACTIVE' };
        profile = await User.ProfileModule.updateOne(toUpdateProfileParams, { id: profile.id }, profileSelect);
        await User.ProfileStatusModule.updateOne({ deletionInitiatedAt: null }, { profileId: profile.id });
      }
    }

    let appVersionResult: Awaited<ReturnType<typeof AppModule.AppVersionModule.fetchByIdVersionPlatform>> | undefined;
    if (params.platform !== 'web_app' && params.versionNo) {
      appVersionResult = await AppModule.AppVersionModule.fetchByIdVersionPlatform({
        versionNo: params.versionNo,
        platformId: params.platform,
      });

      if (appVersionResult.platformId !== params.platform) {
        throw new AppError('AUTH009');
      }
    }

    if (existingSessionResult?.id) {
      try {
        await prismaPG.session.delete({
          where: { id: existingSessionResult.id },
        });
      } catch (_error) {
        console.warn('Failed to delete existing session:', _error);
      }
    }

    const sessionResult = await SessionModule.createOne(prismaPG, {
      ipAddress: params.ip,
      profileId: profile.id,
      appVersionId: appVersionResult?.id,
      deviceId: params.deviceId,
      platformId: params.platform,
    });

    Promise.all(
      [
        params?.deviceToken
          ? CommunicationModule.SessionModule.createOne({
              deviceToken: params?.deviceToken,
              opr: 'CREATE',
              isActive: true,
              profileId: profile.id,
              sessionId: sessionResult.id,
            })
          : null,
        existingSessionResult?.id
          ? CommunicationModule.SessionModule.deleteOne({
              opr: 'DELETE',
              sessionId: existingSessionResult.id,
            })
          : null,
      ].filter(Boolean),
    );

    const jwtPayload = {
      profileId: profile.id,
      email: profile.email,
      sessionId: sessionResult.id,
    };

    const jwtToken = jwt.sign(jwtPayload, ENV.JWT_SECRET, {
      expiresIn: '60d',
    });

    const isReferred = await ReferralModule.ReferralCodeModule.isReferredProfile(profile.id);

    const authResult: AuthLoginResultI = {
      username: profile?.username,
      name: profile?.name,
      isUsernameSaved: Boolean(profile?.username?.trim()?.length),
      email: profile.email,
      profileId: profile.id,
      designationText: profile.designationText,
      entityText: profile.entityText,
      avatar: profile.avatar,
      isEmailVerified: profileStatus.isEmailVerified,
      isPersonalDetailsSaved: profileStatus.isPersonalDetailsSaved,
      isWorkDetailsSaved: profileStatus.isWorkDetailsSaved,
      token: sessionResult.id,
      jwtToken: jwtToken,
      isPrivacyPolicyAccepted: profileStatus.isPrivacyPolicyAccepted,
      previousStatus,
      isReferred,
    };

    return authResult as AuthLoginResultI;
  },
  logout: async (sessionId: string): Promise<void> => {
    const sessionResult = await SessionModule.updateOne({ isActive: false }, { id: sessionId });
    if (!sessionResult) {
      throw new AppError('AUTH022');
    }
    await CommunicationModule.SessionModule.deleteOne({
      opr: 'DELETE',
      sessionId: sessionResult.id,
    });
  },
};

export default AuthModule;
