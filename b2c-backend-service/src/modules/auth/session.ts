import { prismaPG } from '@config/db';
import AppConfig from '@modules/appConfig';
import { Prisma, Session } from '@prisma/postgres';
import { ModuleE, SubModuleE } from '@prisma/mongodb';
import AppError from '@classes/AppError';
import { PostgresTxnI } from '@interfaces/common/db';
import { AuthSessionAppConfigI } from '@interfaces/appConfig/appConfig';
import { SessionUpdateCoordinatesParamsI, SessionUpdateDeviceTokenParamsI } from '@schemas/auth/session';
import { FastifyStateI } from '@interfaces/common/declaration';
import { UUIDI } from '@schemas/common/common';

const SessionModule = {
  createOne: async (
    txn: PostgresTxnI,
    params: Pick<
      Prisma.SessionUncheckedCreateInput,
      'profileId' | 'ipAddress' | 'platformId' | 'appVersionId' | 'deviceId'
    >,
    select: Prisma.SessionSelect = {
      id: true,
      profileId: true,
    },
  ): Promise<Session> => {
    // For web_app platform, ensure both Platform and AppVersion exist
    let finalAppVersionId = params.appVersionId;
    if (params.platformId === 'web_app') {
      // First, ensure the web_app platform exists
      const existingPlatform = await txn.platform.findUnique({
        where: { id: 'web_app' },
        select: { id: true },
      });

      if (!existingPlatform) {
        // Create the web_app platform if it doesn't exist
        await txn.platform.create({
          data: {
            id: 'web_app',
            url: 'http://localhost:3000',
          },
        });
      }

      // Then, ensure we have a valid appVersionId or create/find one
      if (!finalAppVersionId) {
        let webAppVersion = await txn.appVersion.findFirst({
          where: { platformId: 'web_app', isActive: true },
          select: { id: true },
        });

        if (!webAppVersion) {
          // Create a default web_app AppVersion if it doesn't exist
          webAppVersion = await txn.appVersion.create({
            data: {
              platformId: 'web_app',
              versionNo: '1.0.0',
              isActive: true,
            },
            select: { id: true },
          });
        }

        finalAppVersionId = webAppVersion.id;
      }
    }

    const [authSessionAppConfigResult, activeSessionResult] = await Promise.all([
      // Fetches the Session's config to get the default expiration limit
      AppConfig.AppConfigModule.fetchByIdWithFallback(
        {
          module: ModuleE.AUTH,
          subModule: SubModuleE.SESSION,
        },
        undefined,
        params.platformId,
      ),
      // Fetches any existing session for the same platformId of the user
      txn.session.findFirst({
        select: { id: true },
        where: { profileId: params.profileId, platformId: params.platformId, isActive: true },
      }),
    ]);
    // If an active session for the requested platformId exists then we'll expire that session because we can't have multiple sessions on the same platformId
    if (activeSessionResult) {
      await txn.session.update({
        data: { isActive: false },
        select,
        where: { id: activeSessionResult.id },
      });
    }
    // Create the expiryDate fir the session by adding expiration's limit in milliseconds to the currentDate's milliseconds
    const expiryDate = new Date();
    expiryDate.setMilliseconds(
      expiryDate.getMilliseconds() + (authSessionAppConfigResult as AuthSessionAppConfigI).expiry,
    );
    const createParams: Prisma.SessionUncheckedCreateInput = {
      profileId: params.profileId,
      ipAddress: params.ipAddress,
      platformId: params.platformId,
      deviceId: params.deviceId,
      isActive: true,
      expiryDate,
      appVersionId: finalAppVersionId!,
    };
    // Creates the session
    const sessionResult = await txn.session.create({ data: createParams, select });
    return sessionResult;
  },
  updateOne: async (
    data: Partial<Prisma.SessionUncheckedUpdateInput>,
    filters: Pick<Prisma.SessionWhereUniqueInput, 'id'>,
    throwsError: boolean = true,
  ): Promise<Pick<Session, 'id'>> => {
    const sessionResult = await prismaPG.session.update({
      where: filters,
      data,
      select: { id: true },
    });
    if (throwsError && !sessionResult) {
      throw new AppError('SESS006');
    }
    return sessionResult;
  },
  deleteOne: async (txn: PostgresTxnI, filters: Pick<Prisma.SessionWhereUniqueInput, 'id'>): Promise<void> => {
    let sessionResult = await txn.session.findUnique({
      where: filters,
      select: { id: true },
    });
    if (!sessionResult) {
      throw new AppError('SESS008');
    }
    sessionResult = await txn.session.delete({
      where: filters,
      select: { id: true },
    });
    if (!sessionResult) {
      throw new AppError('SESS006');
    }
    return;
  },
  isActive: async (sessionId: UUIDI): Promise<Pick<Session, 'id'>> => {
    const sessionResult = await prismaPG.session.findUnique({
      where: {
        id: sessionId,
        isActive: true,
      },
    });
    if (!sessionResult) {
      throw new AppError('SESS008');
    }
    return sessionResult;
  },
  updateCoordinates: async (state: FastifyStateI, data: SessionUpdateCoordinatesParamsI): Promise<void> => {
    const sessionId = state.sessionId;
    await SessionModule.isActive(sessionId);
    const _sessionResult = await SessionModule.updateOne(
      { latitude: data.latitude, longitude: data.longitude },
      { id: sessionId },
    );
    return;
  },
  updateDeviceToken: async (state: FastifyStateI, data: SessionUpdateDeviceTokenParamsI): Promise<void> => {
    const sessionId = state.sessionId;
    await SessionModule.isActive(sessionId);
    const _sessionResult = await SessionModule.updateOne({ deviceToken: data.deviceToken }, { id: sessionId });
    return;
  },
};
export default SessionModule;
