import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PAGINATION } from '@consts/common/pagination';
import type { LeaderboardFetchForClientResultI, LeaderboardRawResultI } from '@interfaces/leaderboard/leaderboard';
import type { PaginationI } from '@schemas/common/common';
import type { LeaderboardFetchParamsI } from '@schemas/leaderboard/leaderboard';
import { Prisma } from '@prisma/postgres';
import ProfileModule from '@modules/user/profile';
export const LeaderboardModule = {
  fetch: async (params: LeaderboardFetchParamsI): Promise<LeaderboardFetchForClientResultI[]> => {
    const pagination: PaginationI = { ...PAGINATION, pageSize: 20 };

    if (!params.duration) {
      throw new AppError('LBD001');
    }

    if (!params.type) {
      throw new AppError('LBD002');
    }

    let leaderboardTable: string;
    switch (params.duration) {
      case 'WEEKLY':
        switch (params.type) {
          case 'CONTRIBUTION':
            leaderboardTable = '"leaderboard"."ContributionWeeklyLeaderboard"';
            break;
          case 'QNA_ANSWER':
            leaderboardTable = '"leaderboard"."QnAAnswerWeeklyLeaderboard"';
            break;
          case 'TROUBLESHOOT_ANSWER':
            leaderboardTable = '"leaderboard"."TroubleshootWeeklyLeaderboard"';
            break;
          default:
            throw new AppError('LBD003');
        }
        break;
      case 'OVERALL':
        switch (params.type) {
          case 'CONTRIBUTION':
            leaderboardTable = '"leaderboard"."ContributionOverallLeaderboard"';
            break;
          case 'QNA_ANSWER':
            leaderboardTable = '"leaderboard"."QnAAnswerOverallLeaderboard"';
            break;
          case 'TROUBLESHOOT_ANSWER':
            leaderboardTable = '"leaderboard"."TroubleshootOverallLeaderboard"';
            break;
          default:
            throw new AppError('LBD004');
        }
        break;
      default:
        throw new AppError('LBD001');
    }

    const effectivePagination = { ...pagination, pageSize: 10 };
    const skip = effectivePagination.page > 0 ? (effectivePagination.page - 1) * effectivePagination.pageSize : 0;

    const leaderboardResult = await prismaPG.$queryRaw<LeaderboardRawResultI[]>(Prisma.sql` SELECT
        l."profileId",
        l."score",
        l."rank",
        json_build_object(
          'id', p."id",
          'name', p."name",
          'avatar', p."avatar",
          'designationText', p."designationText",
          'designationAlternativeId', p."designationAlternativeId",
          'designationRawDataId', p."designationRawDataId",
          'entityText', p."entityText",
          'entityId', p."entityId",
          'entityRawDataId', p."entityRawDataId"
        ) as "Profile"
      FROM ${Prisma.raw(leaderboardTable)}  l
      JOIN "user"."Profile" p ON p."id" = l."profileId"
      ORDER BY l."rank" ASC
      LIMIT ${effectivePagination.pageSize} OFFSET ${skip} `);

    const leaderboardEntries: LeaderboardFetchForClientResultI[] = leaderboardResult.map((item) => ({
      profileId: item.profileId,
      score: item.score,
      rank: item.rank,
      Profile: ProfileModule.transformProfile(item.Profile),
    }));
    return leaderboardEntries;
  },
};
