import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { EntityRequestStatusE, EntityMemberRoleE } from '@prisma/postgres';
import type {
  EntityRequestBodyI,
  EntityRequestApprovalQueryI,
  EntityRequestRejectionQueryI,
  EntityRequestRevocationQueryI,
} from '@schemas/company/entityRequest';
import type { FastifyStateI } from '@interfaces/common/declaration';

const EntityRequestModule = {
  createOne: async ({ entityId, entityRawDataId, profileId }: EntityRequestBodyI) => {
    if (!entityId && !entityRawDataId) throw new AppError('ENT013');
 const profileStatus = await prismaPG.profileStatus.findUnique({
      where: { profileId },
      select: { isEmailVerified: true }
    });

    if (!profileStatus?.isEmailVerified) {
      throw new AppError('AUTH029');
    }
    const existingMember = await prismaPG.entityMember.findFirst({
      where: { entityId, entityRawDataId, profileId },
    });
    if (existingMember) throw new AppError('ENT012');



  const previousRequest = await prismaPG.entityRequest.findFirst({
  where: {
    entityId,
    entityRawDataId,
    profileId,
  },
});

if (previousRequest) {
  if (previousRequest.status === EntityRequestStatusE.PENDING) {
    throw new AppError('ENT010');
  }

 const reusableStatuses: EntityRequestStatusE[] = [
  EntityRequestStatusE.REVOKED,
  EntityRequestStatusE.REJECTED,
];

  if (!reusableStatuses.includes(previousRequest.status)) {
    throw new AppError('ENT011');
  }
}


    const result = await prismaPG.$transaction(async (tx) => {
      const request = previousRequest
        ? await tx.entityRequest.update({
           where: { id: previousRequest.id },
            data: { status: EntityRequestStatusE.PENDING },
          })
        : await tx.entityRequest.create({
            data: { entityId, entityRawDataId, profileId, status: EntityRequestStatusE.PENDING },
          });

      return request;
    });

    return result;
  },

  approveRequest: async (state: FastifyStateI, query: EntityRequestApprovalQueryI) => {
    const { profileId, entityId, entityRawDataId } = query;
      const actingProfileId = state.profileId;
    if (!actingProfileId) {
      throw new AppError('AUTH020');
    }



    const actingMember = await prismaPG.entityMember.findFirst({
      where: {
        entityId,
        entityRawDataId,
        profileId: actingProfileId,
      },
    });

    if (!actingMember || actingMember.role !== EntityMemberRoleE.ADMIN) {

      throw new AppError('ENT009');
    }

    const request = await prismaPG.entityRequest.findFirst({
      where: {
        profileId,
        entityId,
        entityRawDataId,
        status: EntityRequestStatusE.PENDING,
      },
    });

    if (!request) throw new AppError('ENT008');
const result = await prismaPG.$transaction(async (tx) => {
  const updatedRequest = await tx.entityRequest.update({
    where: {
      id: request.id,
    },
    data: { status: EntityRequestStatusE.APPROVED },
  });

  await tx.entityMember.create({
    data: {
      entityId,
      entityRawDataId,
      profileId,
      role: EntityMemberRoleE.ADMIN,
    },
  });

  if (entityId) {
    await tx.entity.update({
      where: { id: entityId },
      data: { memberCount: { increment: 1 } },
    });
  } else if (entityRawDataId) {
    await tx.entityRawData.update({
      where: { id: entityRawDataId },
      data: { memberCount: { increment: 1 } },
    });
  }

  return updatedRequest;
});

    return result;
  },

 rejectRequest: async (state: FastifyStateI, query: EntityRequestRejectionQueryI) => {
  const { profileId, entityId, entityRawDataId } = query;
  const actingProfileId= state.profileId ;
if (!actingProfileId) {
      throw new AppError('AUTH020');
    }

  const actingMember = await prismaPG.entityMember.findFirst({
    where: {
      profileId: actingProfileId,
      entityId,
      entityRawDataId,
      role: EntityMemberRoleE.ADMIN,
    },
  });

  if (!actingMember) throw new AppError('ENT009');

  const request = await prismaPG.entityRequest.findFirst({
    where: {
      profileId,
      entityId,
      entityRawDataId,
      status: EntityRequestStatusE.PENDING,
    },
  });

  if (!request) throw new AppError('ENT008');

  const updatedRequest = await prismaPG.entityRequest.update({
    where: { id: request.id },
    data: { status: EntityRequestStatusE.REJECTED },
  });

  return updatedRequest;
},


  revokeRequest: async (query: EntityRequestRevocationQueryI) => {
    const { profileId, entityId, entityRawDataId } = query;

    const request = await prismaPG.entityRequest.findFirst({
      where: {
        profileId,
        entityId,
        entityRawDataId,
        status: EntityRequestStatusE.PENDING,
      },
    });

    if (!request) throw new AppError('ENT008');

    await prismaPG.entityRequest.update({
      where: { id: request.id },
      data: { status: EntityRequestStatusE.REVOKED },
    });
  },
};

export default EntityRequestModule;
