import { prismaPG } from '@config/db';
import type { CursorDataI, NumberNullI } from '@interfaces/common/data';
import type { EntityPeopleFetchManySQLI } from '@interfaces/company/people';
import { ProfileExternalI } from '@interfaces/user/profile';
import User from '@modules/user';
import { Prisma } from '@prisma/postgres';
import type { EntityPeopleFetchManyI } from '@schemas/company/people';

export const PeopleModule = {
  fetchManyEmployee: async ({
    cursorId,
    entity,
    pageSize,
    search,
  }: EntityPeopleFetchManyI): Promise<CursorDataI<ProfileExternalI>> => {
    const sqlResult = await prismaPG.$queryRaw<EntityPeopleFetchManySQLI[]>`
      SELECT DISTINCT ON (u."id")
        u."id",
        u."name",
        u."avatar",
        d."designationAlternativeId",
        d."designationRawDataId",
        x."cursorId",
        x."entityId",
        x."entityRawDataId"
      FROM "career"."Experience" x
      INNER JOIN "user"."Profile" u ON u."id" = x."profileId"
      INNER JOIN "career"."ExperienceDesignation" d ON d."experienceId" = x."id"
      WHERE
        ${entity?.dataType === 'master' ? Prisma.sql`x."entityId"` : Prisma.sql`x."entityRawDataId"`} = ${entity.id}
        ${search ? Prisma.sql`AND u."name" ILIKE ${'%' + search.toLowerCase() + '%'}` : Prisma.empty}
        ${cursorId ? Prisma.sql`AND x."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY
        u."id",
        d."toDate" IS NULL DESC,  -- Current positions first
        d."toDate" DESC,          -- Then most recent end dates
        d."fromDate" DESC         -- Then most recent start dates
      LIMIT ${pageSize + 1}
    `;

    const hasNextPage = sqlResult?.length > pageSize;
    const resultTemp = hasNextPage ? sqlResult?.slice(0, pageSize) : sqlResult;

    const nextCursorId: NumberNullI = hasNextPage ? Number(resultTemp[resultTemp.length - 1].cursorId) : null;

    const people: ProfileExternalI[] = resultTemp?.map((item) => ({
      ...User.ProfileModule.transformProfile(item),
      cursorId: Number(item.cursorId),
    }));
    return {
      data: people,
      nextCursorId,
    };
  },
  fetchManyAlumni: async ({
    cursorId,
    entity,
    pageSize,
    search,
  }: EntityPeopleFetchManyI): Promise<CursorDataI<ProfileExternalI>> => {
    const sqlResult = await prismaPG.$queryRaw<EntityPeopleFetchManySQLI[]>`
      SELECT DISTINCT ON (u."id")
        u."id",
        u."name",
        u."avatar",
        d."designationAlternativeId",
        d."designationRawDataId",
        x."cursorId",
        x."entityId",
        x."entityRawDataId"
      FROM "career"."ProfileEducation" e
      INNER JOIN "user"."Profile" u ON u."id" = e."profileId"
      WHERE
        ${entity?.dataType === 'master' ? Prisma.sql`e."entityId"` : Prisma.sql`e."entityRawDataId"`} = ${entity.id}
        ${search ? Prisma.sql`AND u."name" ILIKE ${'%' + search.toLowerCase() + '%'}` : Prisma.empty}
        ${cursorId ? Prisma.sql`AND x."cursorId" < ${cursorId}` : Prisma.empty}
      ORDER BY
        u."id",
        e."toDate" IS NULL DESC,  -- Current positions first
        e."toDate" DESC,          -- Then most recent end dates
        e."fromDate" DESC         -- Then most recent start dates
      LIMIT ${pageSize + 1}
    `;

    const hasNextPage = sqlResult?.length > pageSize;
    const resultTemp = hasNextPage ? sqlResult?.slice(0, pageSize) : sqlResult;

    const nextCursorId: NumberNullI = hasNextPage ? Number(resultTemp[resultTemp.length - 1].cursorId) : null;

    const people: ProfileExternalI[] = resultTemp?.map((item) => ({
      ...User.ProfileModule.transformProfile(item),
      cursorId: Number(item.cursorId),
    }));
    return {
      data: people,
      nextCursorId,
    };
  },
};
