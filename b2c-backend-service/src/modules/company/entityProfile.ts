import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { PostgresTxnI } from '@interfaces/common/db';
import {
  EntityAboutFetchParamsI,
  EntityAboutUpdateParamsI,
  EntityTabsFetchParamsI,
  EntityTabsUpdateParamsI,
} from '@schemas/company/entityProfile';
import { EntityAboutClientI, EntityTabsClientI } from '@interfaces/company/entityProfile';

export const EntityProfileModule = {
  fetchAbout: async (
    params: EntityAboutFetchParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EntityAboutClientI | null> => {
    if (params.entityId) {
      return await txn.entity.findUnique({
        where: { id: params.entityId },
        select: {
          id: true,
          name: true,
          website: true,
          countryIso2: true,
          type: true,
        },
      });
    } else {
      return (await txn.entityRawData.findUnique({
        where: { id: params.entityRawDataId },
        select: {
          id: true,
          name: true,
          website: true,
          type: true,
        },
      })) as EntityAboutClientI;
    }
  },

  updateAbout: async (
    state: FastifyStateI,
    params: EntityAboutUpdateParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<EntityAboutClientI> => {
    const selfProfileId = state.profileId;
    if (!selfProfileId) throw new AppError('AUTH020');

    const member = await txn.entityMember.findFirst({
      where: {
        profileId: selfProfileId,
        OR: [{ entityId: params.entityId }, { entityRawDataId: params.entityRawDataId }],
      },
      select: { role: true },
    });

    if (!member || !['ADMIN', 'MAINTAINER'].includes(member.role)) {
      throw new AppError('ENT007');
    }

    if (params.entityId) {
      return await txn.entity.update({
        where: { id: params.entityId },
        data: {
          name: params.name,
          website: params.website ?? null,
          countryIso2: params.countryIso2 ?? null,
          type: params.type,
        },
        select: {
          id: true,
          name: true,
          website: true,
          countryIso2: true,
          type: true,
        },
      });
    } else {
      return (await txn.entityRawData.update({
        where: { id: params.entityRawDataId! },
        data: {
          name: params.name,
          website: params.website ?? null,
          type: params.type,
        },
        select: {
          id: true,
          name: true,
          website: true,
          type: true,
        },
      })) as EntityAboutClientI;
    }
  },
  fetchTabs: async (params: EntityTabsFetchParamsI, txn: PostgresTxnI = prismaPG): Promise<EntityTabsClientI> => {
    const tab = await txn.entityTab.findFirst({
      where: {
        entityId: params.entityId,
        entityRawDataId: params.entityRawDataId,
      },
      select: {
        peopleTab: true,
        alumniTab: true,
        jobPostingTab: true,
      },
    });

    return (
      tab ?? {
        peopleTab: false,
        alumniTab: false,
        jobPostingTab: false,
      }
    );
  },

  updateTabs: async (
    state: FastifyStateI,
    params: EntityTabsUpdateParamsI,
    txn: PostgresTxnI = prismaPG,
  ): Promise<void> => {
    const selfProfileId = state.profileId;
    if (!selfProfileId) throw new AppError('AUTH020');

    const member = await txn.entityMember.findFirst({
      where: {
        profileId: selfProfileId,
        OR: [{ entityId: params.entityId }, { entityRawDataId: params.entityRawDataId }],
      },
      select: { role: true },
    });

    if (!member || !['ADMIN', 'MAINTAINER'].includes(member.role)) {
      throw new AppError('ENT007');
    }

    const existing = await txn.entityTab.findFirst({
      where: {
        entityId: params.entityId,
        entityRawDataId: params.entityRawDataId,
      },
      select: { id: true },
    });

    const data = {
      entityId: params.entityId,
      entityRawDataId: params.entityRawDataId,
      peopleTab: params.peopleTab,
      alumniTab: params.alumniTab,
      jobPostingTab: params.jobPostingTab,
    };

    if (existing) {
      await txn.entityTab.update({
        where: { id: existing.id },
        data,
      });
    } else {
      await txn.entityTab.create({
        data,
      });
    }
  },
};
