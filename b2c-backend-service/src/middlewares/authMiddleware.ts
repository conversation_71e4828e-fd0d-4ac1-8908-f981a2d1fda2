import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { PlatformE, type PlatformI } from '@consts/app/platform';
import { ENV } from '@consts/common/env';
import type { HeadersI } from '@interfaces/common/api/middleware';
import type { FastifyRequestI } from '@interfaces/common/declaration';
import AppModule from '@modules/app';
import CommunicationModule from '@modules/communication';
import { UUIDSchema, VersionNoSchema } from '@schemas/common/common';
import { FastifyReply } from 'fastify';

export const authMiddleware = (headers: HeadersI) => {
  if (!headers['x-api-key']) {
    throw new AppError('AUTH011');
  }
  if (headers['x-api-key'] !== ENV.API_KEY) {
    throw new AppError('AUTH012');
  }
  if (!headers['x-platform']) {
    throw new AppError('AUTH013');
  }
  if (!(['android', 'ios', 'web_app'] as PlatformI[]).includes(headers['x-platform'])) {
    throw new AppError('AUTH014');
  }
  if (!headers['x-device-id']) {
    throw new AppError('AUTH015');
  }

  // Make version validation non-mandatory for web_app platform
  if (headers['x-platform'] !== 'web_app') {
    if (!headers['x-version-no']) {
      throw new AppError('AUTH017');
    }
    const { error: versionNoError } = VersionNoSchema.safeParse(headers['x-version-no']);
    if (versionNoError) {
      throw new AppError('AUTH018', versionNoError);
    }
  }

  const { error: platformError } = PlatformE.safeParse(headers['x-platform']);
  if (platformError) {
    throw new AppError('AUTH014');
  }
  const { error: deviceIdError } = UUIDSchema.safeParse(headers['x-device-id']);
  if (deviceIdError) {
    throw new AppError('AUTH016');
  }
};

export const authNoTokenMiddleware = async (request: FastifyRequestI, _reply: FastifyReply) => {
  const headers: HeadersI = request.headers as HeadersI;
  if (isMicroserviceRequest(headers)) {
    return;
  } else {
    authMiddleware(headers);
  }

  if (headers['x-platform'] !== 'web_app') {
    try {
      await AppModule.AppVersionModule.fetchByIdVersionPlatform({
        versionNo: headers['x-version-no'],
        platformId: headers['x-platform'],
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      } else {
        throw new AppError('AUTH017');
      }
    }
  }
};

export const authTokenMiddleware = async (request: FastifyRequestI, _reply: FastifyReply) => {
  if (isMicroserviceRequest(request.headers as HeadersI)) {
    return;
  }
  const authorization = request.headers?.authorization;
  if (!authorization) {
    throw new AppError('AUTH019');
  }
  try {
    const headers: HeadersI = request.headers as HeadersI;
    authMiddleware(headers);
    const token = authorization.split(' ')[1];
    const sessionResult = await prismaPG.session.findUnique({
      where: {
        id: token,
        isActive: true,
      },
      select: {
        id: true,
        profileId: true,
        expiryDate: true,
      },
    });
    if (!sessionResult) {
      throw new AppError('AUTH020');
    }
    const currentDate = new Date();
    if (currentDate >= sessionResult.expiryDate) {
      await prismaPG.session.update({
        data: { isActive: false },
        where: { id: token },
      });
      await CommunicationModule.SessionModule.deleteOne({
        opr: 'DELETE',
        sessionId: sessionResult.id,
      });
      throw new AppError('AUTH021');
    } else {
      request.profileId = sessionResult.profileId;
      request.sessionId = sessionResult.id;
    }
  } catch (_error) {
    throw new AppError('AUTH020');
  }
};

export const isMicroserviceRequest = (headers: HeadersI): boolean => {
  if (headers?.['x-api-microservice-key']) {
    if (headers['x-api-microservice-key'] !== ENV.API_MICROSERVICE_KEY) {
      throw new AppError('AUTH012');
    }
    return true;
  }
  return false;
};
