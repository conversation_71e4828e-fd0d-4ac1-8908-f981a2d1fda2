import { FastifyInstance } from 'fastify';
import postRoutes from './post';
import reactionRoutes from './reaction';
import commentRoutes from './comment';
import entityPostRoutes from './entityPost';

const feedRoutes = (fastify: FastifyInstance): void => {
  fastify.register(postRoutes);
  fastify.register(reactionRoutes);
  fastify.register(commentRoutes);
  fastify.register(entityPostRoutes);
};

export default feedRoutes;
