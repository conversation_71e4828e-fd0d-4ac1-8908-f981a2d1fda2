import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { EntityPostCreateOneSchema, EntityPostFetchManySchema } from '@schemas/feed/entityPost';
import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import FeedModule from '@modules/feed';

const entityPostRoutes = (fastify: FastifyInstance): void => {

  fastify.post('/backend/api/v1/company/entity-post', {  }, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: bodyError, data: bodyData } = EntityPostCreateOneSchema.safeParse(request.body);
    if (bodyError) throw new AppError('ENT006', bodyError);

    const result = await FeedModule.EntityPostModule.createOne(request, bodyData);
    reply.status(HttpStatus.CREATED).send(result);
  });

  fastify.get('/backend/api/v1/company/entity-post', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityPostFetchManySchema.safeParse(request.query);
    if (queryError) throw new AppError('ENT005', queryError);

    const result = await FeedModule.EntityPostModule.fetchMany(queryData);
    reply.status(HttpStatus.OK).send(result);
  });

};

export default entityPostRoutes;
