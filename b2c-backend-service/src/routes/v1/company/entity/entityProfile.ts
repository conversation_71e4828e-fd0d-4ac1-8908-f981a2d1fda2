import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import {
  EntityAboutFetchSchema,
  EntityAboutUpdateSchema,
  EntityTabsFetchSchema,
  EntityTabsUpdateSchema,
} from '@schemas/company/entityProfile';

const entityProfileRoutes = (fastify: FastifyInstance): void => {
  fastify.get(
    '/backend/api/v1/company/entity-profile/about',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityAboutFetchSchema.safeParse(request.query);
      if (queryError) throw new AppError('ENT005', queryError);

      const result = await Company.EntityProfileModule.fetchAbout(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.patch(
    '/backend/api/v1/company/entity-profile/about',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = EntityAboutUpdateSchema.safeParse(request.body);
      if (bodyError) throw new AppError('ENT006', bodyError);

      const result = await Company.EntityProfileModule.updateAbout(request, bodyData);
      reply.status(HttpStatus.OK).send(result);
    },
  );
  fastify.get(
    '/backend/api/v1/company/entity-profile/tabs',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: queryError, data: queryData } = EntityTabsFetchSchema.safeParse(request.query);
      if (queryError) throw new AppError('ENT005');

      const result = await Company.EntityProfileModule.fetchTabs(queryData);
      reply.status(HttpStatus.OK).send(result);
    },
  );

  fastify.patch(
    '/backend/api/v1/company/entity-profile/tabs',
    {},
    async (request: FastifyRequestI, reply: FastifyReply) => {
      const { error: bodyError, data: bodyData } = EntityTabsUpdateSchema.safeParse(request.body);
      if (bodyError) throw new AppError('ENT006');

      await Company.EntityProfileModule.updateTabs(request, bodyData);
      reply.status(HttpStatus.NO_CONTENT).send();
    },
  );
};

export default entityProfileRoutes;
