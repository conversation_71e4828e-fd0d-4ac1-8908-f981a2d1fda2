import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Company from '@modules/company';
import { FastifyInstance, FastifyReply } from 'fastify';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { EntityPeopleFetchManySchema } from '@schemas/company/people';

const entityPeopleRoutes = (fastify: FastifyInstance): void => {
  fastify.get('/backend/api/v1/company/people/employees', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityPeopleFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('ENPL001', queryError);
    }

    const result = await Company.PeopleModule.fetchManyEmployee(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
  fastify.get('/backend/api/v1/company/people/alumni', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { error: queryError, data: queryData } = EntityPeopleFetchManySchema.safeParse(request.query);
    if (queryError) {
      throw new AppError('ENPL001', queryError);
    }

    const result = await Company.PeopleModule.fetchManyAlumni(queryData);
    reply.status(HttpStatus.OK).send(result);
  });
};

export default entityPeopleRoutes;
