import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import { FastifyRequestI } from '@interfaces/common/declaration';
import { ReferralSchema, ReferralStatusSchema } from '@schemas/referral/referral';
import { ReferralModule } from '@modules/referral';

import { FastifyInstance, FastifyReply } from 'fastify';
import { PaginationSchema } from '@schemas/common/common';

const referralRoutes = (fastify: FastifyInstance): void => {
  fastify.post('/backend/api/v1/referral/code', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ReferralSchema.safeParse(request.body);
    if (error) throw new AppError('REF001', error);

    const code = await ReferralModule.ReferralCodeModule.ensureReferralCode(data.profileId);
    reply.status(HttpStatus.OK).send({ code });
  });

  fastify.post('/backend/api/v1/referral/status', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ReferralStatusSchema.safeParse(request.body);
    if (error) throw new AppError('REF002', error);

    const status = await ReferralModule.ReferralCodeModule.createReferralStatus(
      data.referredProfileId,
      data.referralCode,
    );
    reply.status(HttpStatus.OK).send(status);
  });

  fastify.patch('/backend/api/v1/referral/onboarding', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const { data, error } = ReferralStatusSchema.pick({ referredProfileId: true }).safeParse(request.body);
    if (error) throw new AppError('REF003', error);

    const updated = await ReferralModule.ReferralCodeModule.completeOnboarding(data.referredProfileId);
    reply.status(HttpStatus.OK).send(updated);
  });

  fastify.get('/backend/api/v1/referral/referred', {}, async (request: FastifyRequestI, reply: FastifyReply) => {
    const parsed = PaginationSchema.safeParse(request.query);

    if (!parsed.success) {
      throw new AppError('REF001', parsed.error);
    }

    const { page, pageSize } = parsed.data;

    const result = await ReferralModule.ReferralCodeModule.getRewardedReferralsByReferrerId(
      request.profileId,
      Number(page),
      Number(pageSize),
    );

    reply.status(HttpStatus.OK).send(result);
  });
};

export default referralRoutes;
