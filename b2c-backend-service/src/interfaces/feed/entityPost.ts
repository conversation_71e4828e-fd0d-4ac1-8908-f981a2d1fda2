import type {  PostMedia } from '@prisma/postgres';
import { ProfileExternalI } from '@interfaces/user/profile';

export type EntityPostDataClientI = {
  id: string;
  caption: string;
  shortCaption?: string;
  isCaptionTruncated?: boolean;
  cursorId?: number| bigint ;
  reactionsCount: number;
  totalCommentsCount: number;
  isLiked: boolean;
  entity?: {
    id: string;
    name: string;
    avatar: string | null;
    type: 'master' | 'raw';

  };
  Profile: ProfileExternalI;
  Media: Pick<PostMedia, 'caption' | 'fileUrl'>[];
};

export type EntityPostExternalClientI = Omit<EntityPostDataClientI, 'shortCaption' | 'isCaptionTruncated'>;

export type EntityPostFetchManyResultI = {
  posts: EntityPostExternalClientI[];
  cursorId?: number;
};

