import { prismaPG } from '../../../src/config/db';
export const migrateMaterializedView = async () => {
  await prismaPG.$transaction(async (txn) => {

    await txn.$executeRaw`
      CREATE SCHEMA IF NOT EXISTS "leaderboard";
    `;

    await txn.$executeRawUnsafe(`
      DO $$
      BEGIN
        -- ContributionWeeklyLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."ContributionWeeklyLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."ContributionWeeklyLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."ContributionWeeklyLeaderboard" CASCADE';
          END;
        END;

        -- QnAAnswerWeeklyLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."QnAAnswerWeeklyLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."QnAAnswerWeeklyLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."QnAAnswerWeeklyLeaderboard" CASCADE';
          END;
        END;

        -- TroubleshootWeeklyLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."TroubleshootWeeklyLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."TroubleshootWeeklyLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."TroubleshootWeeklyLeaderboard" CASCADE';
          END;
        END;

        -- TotalWeeklyLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."TotalWeeklyLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."TotalWeeklyLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."TotalWeeklyLeaderboard" CASCADE';
          END;
        END;

        -- ContributionOverallLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."ContributionOverallLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."ContributionOverallLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."ContributionOverallLeaderboard" CASCADE';
          END;
        END;

        -- QnAAnswerOverallLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."QnAAnswerOverallLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."QnAAnswerOverallLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."QnAAnswerOverallLeaderboard" CASCADE';
          END;
        END;

        -- TroubleshootOverallLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."TroubleshootOverallLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."TroubleshootOverallLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."TroubleshootOverallLeaderboard" CASCADE';
          END;
        END;

        -- TotalOverallLeaderboard
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."TotalOverallLeaderboard" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."TotalOverallLeaderboard" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."TotalOverallLeaderboard" CASCADE';
          END;
        END;

        -- MaxScores
        BEGIN
          EXECUTE 'DROP MATERIALIZED VIEW IF EXISTS leaderboard."MaxScores" CASCADE';
        EXCEPTION WHEN OTHERS THEN
          BEGIN
            EXECUTE 'DROP VIEW IF EXISTS leaderboard."MaxScores" CASCADE';
          EXCEPTION WHEN OTHERS THEN
            EXECUTE 'DROP TABLE IF EXISTS leaderboard."MaxScores" CASCADE';
          END;
        END;
      END $$;
    `);

    (await Promise.all([
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."ContributionWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."contributionScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."contributionScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (NOW() - INTERVAL '7 days')
      ORDER BY
        p."contributionScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,

      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."QnAAnswerWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."qnaAnswerScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."qnaAnswerScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (NOW() - INTERVAL '7 days')
      ORDER BY
        p."qnaAnswerScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TroubleshootWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."troubleshootAnswerScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."troubleshootAnswerScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (NOW() - INTERVAL '7 days')
      ORDER BY
        p."troubleshootAnswerScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
      CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TotalWeeklyLeaderboard"
      AS
      SELECT
        p."profileId",
        p."totalScore" AS score,
        ROW_NUMBER() OVER (ORDER BY p."totalScore" DESC) AS rank
      FROM "score"."RewardProfile" p
      WHERE
        p."updatedAt" >= (NOW() - INTERVAL '7 days')
      ORDER BY
        p."totalScore" DESC, p."updatedAt"  DESC
      LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."ContributionOverallLeaderboard"
        AS
        SELECT
          p."profileId",
          p."contributionScore" AS score,
          ROW_NUMBER() OVER (ORDER BY p."contributionScore" DESC) AS rank
        FROM
          "score"."RewardProfile" p
        ORDER BY p."contributionScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
          CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."QnAAnswerOverallLeaderboard"
        AS
        SELECT
          p."profileId",
          p."qnaAnswerScore" AS score,
          ROW_NUMBER() OVER (ORDER BY p."qnaAnswerScore" DESC) AS rank
        FROM
          "score"."RewardProfile" p
        ORDER BY p."qnaAnswerScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TroubleshootOverallLeaderboard"
        AS
        SELECT
          p."profileId",
          p."troubleshootAnswerScore" AS score,
         ROW_NUMBER() OVER (ORDER BY p."troubleshootAnswerScore" DESC) AS rank
        FROM
          "score"."RewardProfile" p
        ORDER BY p."troubleshootAnswerScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
          CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."TotalOverallLeaderboard"
        AS
        SELECT
          p."profileId",
          p."totalScore" AS score,
          ROW_NUMBER() OVER (ORDER BY p."totalScore" DESC) AS rank
        FROM
          "score"."RewardProfile" p
        ORDER BY p."totalScore" DESC
        LIMIT 20
      `,
      txn.$executeRaw`
        CREATE MATERIALIZED VIEW IF NOT EXISTS "leaderboard"."MaxScores"
        AS
        SELECT
          MAX("qnaAnswerScore") as "maxQnaAnswerScore",
          MAX("troubleshootAnswerScore") as "maxTroubleshootAnswerScore",
          MAX("contributionScore") as "maxContributionScore",
          MAX("totalScore") as "maxTotalScore"
        FROM "score"."RewardProfile"
      `,

    ]))

  })

}
