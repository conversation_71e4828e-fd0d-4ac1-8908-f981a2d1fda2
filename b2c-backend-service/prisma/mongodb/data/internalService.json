[{"id": "684b5edbc277ee59d32cab8c", "name": "COMMUNICATION", "config": {"baseURL": "http://communication-service:4001", "apiMicroserviceKey": "sUxtxLvhOdSHpto2xNFei1IFVxjg0BYbuPkls5eo4h0="}, "credsConfig": "", "createdAt": "2025-04-13T08:45:39.004Z", "updatedAt": "2025-04-13T08:45:39.004Z"}, {"id": "6875f7ada275ff177f1bdbdf", "name": "BACKEND", "config": {"baseURL": "http://backend-service:4000", "apiMicroserviceKey": "sUxtxLvhOdSHpto2xNFei1IFVxjg0BYbuPkls5eo4h0="}, "credsConfig": "", "createdAt": "2025-04-13T08:45:39.004Z", "updatedAt": "2025-04-13T08:45:39.004Z"}]