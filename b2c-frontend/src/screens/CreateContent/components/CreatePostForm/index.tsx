import { useEffect, useState, useRef } from 'react';
import {
  Pressable,
  Text,
  View,
  Image,
  ActivityIndicator,
  ScrollView,
  InteractionManager,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  TextInput,
  Modal,
  Dimensions,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import Config from 'react-native-config';
import EventSource from 'react-native-sse';
import BottomSheet from '@/src/components/Bottomsheet';
import Button from '@/src/components/Button';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import RateLimitModal from '@/src/components/RateLimitModal';
import VideoPlayer from '@/src/components/VideoPlayer';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Attachment from '@/src/assets/svgs/Attachment';
import Close from '@/src/assets/svgs/Close';
import PlayIcon from '@/src/assets/svgs/Play';
import VideoPreview from '@/src/assets/svgs/VideoPreview';
import useStorage from '@/src/hooks/storage';
import CustomPromptModal from '../CustomPrompt';
import PostThumbnail from '../PostThumbnail';
import type { CreatePostFormProps } from './types';
import useCreatePostForm from './useHook';

const AI_URL = Config.AI_URL;
const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const getRandomPrompts = (count: number) => {
  const prompts = [
    'Share insights about sustainable shipping practices and their environmental impact on marine ecosystems',
    'Discuss cutting-edge maritime technology innovations that are revolutionizing the shipping industry',
    'Explain comprehensive maritime safety protocols and advanced crew training standards',
    'Analyze emerging trends in port automation and digital transformation initiatives',
    'Describe diverse career opportunities in marine engineering and naval architecture fields',
    'Discuss the critical role of maritime law in facilitating international trade and commerce',
    'Share expert thoughts on green shipping initiatives and alternative fuel technologies',
    'Explain the strategic significance of maritime logistics in optimizing global supply chains',
    'Discuss current challenges and innovative solutions in modern shipbuilding and vessel design',
    'Analyze the transformative impact of digitalization on maritime operations and operational efficiency',
    'Share comprehensive knowledge about marine environmental protection and conservation efforts',
    'Discuss the technological evolution of maritime communication and navigation systems',
    'Explain the paramount importance of maritime security measures in international waters',
    'Analyze progressive trends in offshore renewable energy and sustainable marine resource utilization',
    'Share valuable insights about maritime education pathways and professional development opportunities',
    'Discuss the economic impact of maritime trade on global commerce and regional development',
    'Explain advanced vessel maintenance strategies and predictive maintenance technologies',
    'Share knowledge about maritime risk management and insurance best practices',
  ];

  return prompts.sort(() => 0.5 - Math.random()).slice(0, count);
};

const CreatePostForm = ({
  onSuccess,
  type,
  portUnLocode,
  editing,
  postId,
}: CreatePostFormProps) => {
  const {
    caption,
    setCaption,
    isSubmitting,
    isLoading,
    textInputRef,
    handlePost,
    handleAttachments,
    isCaptioning,
    media,
    handleDeletePost,
    handleDone,
    handlePostThumbnail,
    handlePreview,
    isDone,
    selectedImage,
    postText,
    setPostText,
    setMedia,
    setIsCaptioning,
    isAttachmentOptionsVisible,
    setIsAttachmentOptionsVisible,
    handleModalHide,
    handleGalleryPress,
    handleCameraPress,
  } = useCreatePostForm({ onSuccess, type, portUnLocode, editing, postId });

  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [randomPrompts, setRandomPrompts] = useState<string[]>([]);
  const [aiMode, setAiMode] = useState<'global' | 'image'>('global');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showRateLimitModal, setShowRateLimitModal] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [displayText, setDisplayText] = useState('');
  const [finalText, setFinalText] = useState('');

  const { getStorage } = useStorage();
  const eventSourceRef = useRef<EventSource | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation = useNavigation<BottomTabNavigationI>();
  const isStreamingRef = useRef(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (!finalText || !isStreamingRef.current) {
      return;
    }

    let currentIndex = 0;
    setDisplayText('');

    const animateText = () => {
      if (currentIndex < finalText.length && isStreamingRef.current) {
        const nextIndex = Math.min(currentIndex + 3, finalText.length);
        setDisplayText(finalText.slice(0, nextIndex));
        currentIndex = nextIndex;

        if (currentIndex < finalText.length) {
          animationFrameRef.current = requestAnimationFrame(() => {
            setTimeout(animateText, 30);
          });
        } else {
          isStreamingRef.current = false;
          const maxLength = aiMode === 'image' ? 255 : type === 'USER_POST' ? 2000 : 1000;
          const truncatedText = finalText.slice(0, maxLength);

          if (aiMode === 'image') {
            setPostText(truncatedText);
          } else {
            setCaption(truncatedText);
          }

          setDisplayText('');
          setFinalText('');
        }
      }
    };

    animateText();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [finalText, aiMode, type, setCaption, setPostText]);

  const generateContent = async (prompt: string, mode: 'global' | 'image') => {
    setAiMode(mode);
    setIsGenerating(true);
    setDisplayText('');
    setFinalText('');
    isStreamingRef.current = false;

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    const maxChars = mode === 'image' ? 150 : type === 'USER_POST' ? 1500 : 800;
    const promptType = mode === 'image' ? 'image caption' : 'social media post';

    const enhancedPrompt = `Create a professional, engaging, and informative maritime-focused ${promptType} (maximum ${maxChars} characters). The content should be industry-relevant, add significant value to readers, and include appropriate hashtags. Make it compelling and educational. Focus on: ${prompt}`;

    const url = `${AI_URL}/query/general?query=${encodeURIComponent(enhancedPrompt)}&device=${await getStorage('deviceToken')}`;

    const es = new EventSource(url);
    eventSourceRef.current = es;

    let accumulatedText = '';

    es.addEventListener('open', () => {
      //
    });

    es.addEventListener('message', (event) => {
      try {
        const parsed = JSON.parse(event.data ?? '');
        const text = parsed.text;

        if (text === '[DONE]') {
          es.close();
          eventSourceRef.current = null;
          setIsGenerating(false);
          if (accumulatedText.trim()) {
            setFinalText(accumulatedText);
            isStreamingRef.current = true;
          }
          return;
        }

        accumulatedText += text;
      } catch (err) {
        console.error('Failed to parse SSE JSON:', event.data);
      }
    });

    es.addEventListener('error', () => {
      es.close();
      eventSourceRef.current = null;
      setIsGenerating(false);
      setDisplayText('');
      setFinalText('');
      isStreamingRef.current = false;
      setShowRateLimitModal(true);
    });

    try {
      const response = await fetch(url, { method: 'HEAD' });
      if (response.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    } catch (error: unknown) {
      const fetchError = error as { status?: number };
      if (fetchError && fetchError.status === 429) {
        es.close();
        eventSourceRef.current = null;
        setIsGenerating(false);
        setShowRateLimitModal(true);
        return;
      }
    }
  };

  const paraphraseContent = async (text: string, mode: 'global' | 'image') => {
    const maxChars = mode === 'image' ? 150 : type === 'USER_POST' ? 1500 : 800;
    const paraphrasePrompt = `Improve and refine this maritime content by: 1) Correcting any grammar and spelling errors, 2) Optimizing vocabulary for professional maritime industry standards, 3) Enhancing clarity and readability, 4) Adding more value and depth to the topic, 5) Ensuring it stays focused on the maritime theme, 6) Including relevant industry hashtags. Keep it under ${maxChars} characters and make it more engaging and informative than the original: "${text}"`;

    await generateContent(paraphrasePrompt, mode);
  };

  const stopGeneration = () => {
    setIsGenerating(false);

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    isStreamingRef.current = false;

    if (displayText.trim()) {
      const maxLength = aiMode === 'global' ? (type === 'USER_POST' ? 2000 : 1000) : 255;
      const truncatedText = displayText.slice(0, maxLength);

      if (aiMode === 'global') {
        setCaption(truncatedText);
      } else {
        setPostText(truncatedText);
      }
    }

    setDisplayText('');
    setFinalText('');
  };

  const focusTextInput = () => {
    if (textInputRef.current && !isLoading) {
      if (Platform.OS === 'android') {
        InteractionManager.runAfterInteractions(() => {
          setTimeout(() => {
            textInputRef.current?.focus();
          }, 200);
        });
      } else {
        requestAnimationFrame(() => {
          textInputRef.current?.focus();
        });
      }
    }
  };

  const resetForm = () => {
    if (textInputRef.current) {
      textInputRef.current.clear();
    }
    setCaption('');
    setPostText('');
    setMedia([]);
    setIsCaptioning(false);
  };

  const handleClose = () => {
    resetForm();
    Keyboard.dismiss();
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  const handleImageCaptionChange = (text: string) => {
    if (text.length <= 255) {
      setPostText(text);
    }
  };

  const handleGlobalCaptionChange = (text: string) => {
    if (text.length <= (type === 'USER_POST' ? 2000 : 1000)) {
      setCaption(text);
    }
  };

  const handleShowAIOptions = (mode: 'global' | 'image') => {
    setAiMode(mode);
    setRandomPrompts(getRandomPrompts(8));
    setShowAIModal(true);
  };

  const handleRefreshPrompts = () => {
    setRandomPrompts(getRandomPrompts(8));
  };

  const handlePromptSelect = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIModal(false);
  };

  const handleCustomPrompt = (prompt: string) => {
    generateContent(prompt, aiMode);
    setShowAIModal(false);
  };

  const handleParaphrase = () => {
    const currentText = isCaptioning ? postText : caption;
    if (currentText.trim()) {
      paraphraseContent(currentText, isCaptioning ? 'image' : 'global');
    }
  };

  const handleMediaPreview = (post: any) => {
    if (handlePreview && typeof handlePreview === 'function') {
      handlePreview(post);
      setIsCaptioning(true);
    }
  };

  const handleThumbnailPress = (uri: string) => {
    if (handlePostThumbnail && typeof handlePostThumbnail === 'function') {
      handlePostThumbnail(uri);
    }
  };

  useFocusEffect(() => {
    const timer = setTimeout(
      () => {
        focusTextInput();
      },
      Platform.OS === 'android' ? 400 : 100,
    );

    return () => clearTimeout(timer);
  });

  useEffect(() => {
    const unsubscribe = navigation.addListener('blur', () => {
      if (selectedImage && postText) {
        handlePostThumbnail(selectedImage);
      }
    });

    return unsubscribe;
  }, [navigation, selectedImage, postText]);

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  if (isLoading) {
    return (
      <View className="flex-1 bg-white justify-center items-center">
        <ActivityIndicator size="small" color="#448600" />
        <Text className="mt-2 text-gray-600">Loading post...</Text>
      </View>
    );
  }

  const maxLength = type === 'USER_POST' ? 2000 : 1000;
  const hasGlobalText = caption.trim().length > 0;
  const hasImageText = postText.trim().length > 0;
  const hasMedia = media.length > 0 && isDone && !isCaptioning;
  const bottomButtonHeight = 60;

  const shouldApplyKeyboardAdjustment = Platform.OS === 'android' && Platform.Version >= 35;
  const adjustedKeyboardHeight = shouldApplyKeyboardAdjustment ? keyboardHeight : 0;

  const getTextInputValue = () => {
    if (isStreamingRef.current) {
      return displayText;
    }
    return isCaptioning ? postText : caption;
  };

  const getPlaceholderText = () => {
    if (isGenerating) return 'AI is thinking...';
    if (isStreamingRef.current) return 'AI is writing...';
    return isCaptioning ? 'Describe this image' : "What's on your mind?";
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      className="flex-1"
    >
      <View className="flex-1 bg-white">
        <View className="flex-row justify-between items-center px-4 py-2 border-b border-gray-100">
          <View className="flex-row items-center">
            <Pressable
              onPress={handleClose}
              className="rounded-full active:bg-gray-100 p-1"
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            >
              <Close height={2.5} width={2.5} />
            </Pressable>
            <Text className="text-lg font-semibold ml-2 text-gray-900">
              {type === 'USER_POST'
                ? editing
                  ? 'Edit Post'
                  : 'Create Post'
                : editing
                  ? 'Edit Note'
                  : 'Create Note'}
            </Text>
          </View>
          <View className="flex-row gap-2 items-center">
            {type === 'USER_POST' && (
              <>
                <Pressable className="opacity-50" onPress={handleAttachments}>
                  <Attachment />
                </Pressable>
              </>
            )}
            {isCaptioning ? (
              <Pressable onPress={handleDone}>
                <Text className="text-primaryGreen font-medium">Done</Text>
              </Pressable>
            ) : (
              <Button
                className="w-auto px-8 rounded-full"
                label={editing ? 'Update' : 'Post'}
                onPress={handlePost}
                loading={isSubmitting}
                disabled={isSubmitting || !caption.trim()}
              />
            )}
          </View>
        </View>

        <View
          className="flex-1"
          style={{ paddingBottom: bottomButtonHeight + adjustedKeyboardHeight }}
        >
          {!isCaptioning && (
            <View className="flex-1 px-4 pt-2">
              <View className="relative flex-1">
                <TextInput
                  ref={textInputRef}
                  value={getTextInputValue()}
                  onChangeText={handleGlobalCaptionChange}
                  placeholder={getPlaceholderText()}
                  placeholderTextColor="#9ca3af"
                  multiline={true}
                  textAlignVertical="top"
                  autoFocus
                  maxLength={maxLength}
                  editable={!isGenerating && !isStreamingRef.current}
                  blurOnSubmit={false}
                  returnKeyType={Platform.OS === 'ios' ? 'default' : 'none'}
                  scrollEnabled={true}
                  className="flex-1 text-base text-black leading-[22px] p-3 pb-9 text-top min-h-[200px]"
                />

                {(isGenerating || isStreamingRef.current) && (
                  <View className="absolute top-2 right-2">
                    <Pressable
                      onPress={stopGeneration}
                      className="bg-red-500 px-2 py-1 rounded-full"
                      hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                    >
                      <Text className="text-white text-xs font-medium">Stop</Text>
                    </Pressable>
                  </View>
                )}

                <View className="absolute bottom-2 right-3">
                  <View className="bg-gray-100 px-2 py-1 rounded-full">
                    <Text
                      className={`text-xs ${caption.length > maxLength * 0.9 ? 'text-orange-500' : 'text-gray-500'}`}
                    >
                      {caption.length}/{maxLength}
                    </Text>
                  </View>
                </View>
              </View>

              {hasMedia && (
                <ScrollView
                  className="max-h-40 mt-2 flex-grow-0 flex-shrink"
                  contentContainerStyle={{
                    flexGrow: 0,
                    paddingBottom: media.length === 1 ? 0 : 8,
                  }}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                  nestedScrollEnabled={true}
                >
                  {media.map((post, index) => {
                    const isVideo = post.type?.startsWith('video/') || false;

                    return (
                      <Pressable
                        key={`media-${index}-${post.uri}`}
                        onPress={() => handleMediaPreview(post)}
                        className="flex-row gap-3 w-full mb-2 p-3 bg-gray-50 rounded-lg"
                        hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                      >
                        <View
                          style={{ width: 150, height: 120, borderRadius: 5, overflow: 'hidden' }}
                        >
                          {isVideo ? (
                            <View style={{ position: 'relative', width: '100%', height: '100%' }}>
                              <VideoPlayer
                                source={post.uri}
                                width={150}
                                height={120}
                                borderRadius={5}
                                showControls={false}
                                muted={true}
                                controlButtonSize={12}
                              />
                              <View
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  backgroundColor: 'rgba(0, 0, 0, 0.3)',
                                }}
                              >
                                <PlayIcon width={10} height={10} fill="white" />
                              </View>
                            </View>
                          ) : (
                            <Image
                              source={{ uri: post.uri }}
                              style={{ width: '100%', height: '100%' }}
                              resizeMode="cover"
                            />
                          )}
                        </View>
                        <View className="flex-1">
                          <Text
                            className="leading-5 text-sm text-labelBlack"
                            numberOfLines={3}
                            ellipsizeMode="tail"
                          >
                            {post.caption}
                          </Text>
                          {isVideo && (
                            <Text className="text-xs text-gray-500 mt-1">
                              Video • {post.filename}
                            </Text>
                          )}
                        </View>
                      </Pressable>
                    );
                  })}
                </ScrollView>
              )}
              {(isGenerating || isStreamingRef.current) && (
                <View className="pt-1">
                  <Text className="text-xs text-gray-500">
                    {isGenerating
                      ? 'AI is crafting valuable maritime content...'
                      : 'AI is writing your post...'}
                  </Text>
                </View>
              )}
            </View>
          )}

          {isCaptioning && (
            <ScrollView
              ref={scrollViewRef}
              className="flex-1"
              contentContainerStyle={{ flexGrow: 1, paddingBottom: 8 }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
              bounces={true}
            >
              <View className="flex-1 px-4 pt-2">
                <View className="items-center justify-center mb-3">
                  {(() => {
                    const selectedMedia = media.find((m) => m.uri === selectedImage);
                    const isVideo = selectedMedia?.type?.startsWith('video/') || false;

                    return isVideo ? (
                      <View
                        style={{ width: 280, height: 180, borderRadius: 8, overflow: 'hidden' }}
                      >
                        <VideoPlayer
                          source={selectedImage!}
                          width={280}
                          height={180}
                          borderRadius={8}
                          showControls={true}
                          muted={true}
                          controlButtonSize={8}
                        />
                      </View>
                    ) : (
                      <Image
                        source={{ uri: selectedImage! }}
                        style={{ width: 280, height: 180 }}
                        resizeMode="contain"
                        className="rounded-md"
                      />
                    );
                  })()}
                  <View className="flex-row flex-wrap justify-center gap-2 mt-2">
                    {media.map((post, index) => (
                      <PostThumbnail
                        key={index}
                        uri={post.uri}
                        type={post.type}
                        isSelected={post.uri === selectedImage}
                        onPress={handleThumbnailPress}
                        handleTrash={handleDeletePost}
                      />
                    ))}
                  </View>
                </View>

                <View className="relative flex-1">
                  <TextInput
                    ref={textInputRef}
                    value={getTextInputValue()}
                    onChangeText={handleImageCaptionChange}
                    placeholder={getPlaceholderText()}
                    placeholderTextColor="#9ca3af"
                    multiline={true}
                    textAlignVertical="top"
                    autoFocus
                    maxLength={255}
                    editable={!isGenerating && !isStreamingRef.current}
                    returnKeyType={Platform.OS === 'ios' ? 'default' : 'none'}
                    scrollEnabled={true}
                    className="flex-1 text-base text-black leading-[22px] p-3 pb-9 text-top min-h-[200px] border border-gray-200 rounded-lg"
                  />

                  {(isGenerating || isStreamingRef.current) && (
                    <View className="absolute top-2 right-2">
                      <Pressable
                        onPress={stopGeneration}
                        className="bg-red-500 px-2 py-1 rounded-full"
                        hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                      >
                        <Text className="text-white text-xs font-medium">Stop</Text>
                      </Pressable>
                    </View>
                  )}

                  <View className="absolute bottom-2 right-3">
                    <View className="bg-gray-100 px-2 py-1 rounded-full">
                      <Text
                        className={`text-xs ${postText.length > 255 * 0.9 ? 'text-orange-500' : 'text-gray-500'}`}
                      >
                        {postText.length}/255
                      </Text>
                    </View>
                  </View>
                </View>

                {(isGenerating || isStreamingRef.current) && (
                  <View className="pt-1">
                    <Text className="text-xs text-gray-500">
                      {isGenerating ? 'AI is analyzing...' : 'AI is writing caption...'}
                    </Text>
                  </View>
                )}
              </View>
            </ScrollView>
          )}
        </View>

        <View
          className="absolute left-0 right-0 bg-white border-t border-gray-100 py-1.5 px-4"
          style={{
            bottom: adjustedKeyboardHeight,
            paddingBottom: keyboardHeight ? 8 : 20,
          }}
        >
          <View className="flex-row gap-2">
            <Pressable
              onPress={() => handleShowAIOptions(isCaptioning ? 'image' : 'global')}
              className="flex-1 flex-row items-center justify-center py-2.5 px-3 bg-violet-50 rounded-lg border border-violet-200"
              hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
            >
              <Text className="text-violet-600 font-medium text-sm">⚓ Generate AI</Text>
            </Pressable>

            {((isCaptioning && hasImageText) || (!isCaptioning && hasGlobalText)) && (
              <Pressable
                onPress={handleParaphrase}
                className="flex-1 flex-row items-center justify-center py-2.5 px-3 bg-green-50 rounded-lg border border-green-200"
                hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
              >
                <Text className="text-green-600 font-medium text-sm">✨ Improve</Text>
              </Pressable>
            )}
          </View>
        </View>

        <Modal
          visible={showAIModal}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowAIModal(false)}
        >
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            className="flex-1"
            style={{ maxHeight: SCREEN_HEIGHT * 0.9 }}
          >
            <View className="flex-1 bg-white">
              <View className="flex-row justify-between items-center px-4 py-3 border-b border-gray-200">
                <Text className="text-lg font-semibold text-gray-900">
                  Generate Maritime Content
                </Text>
                <Pressable
                  onPress={() => setShowAIModal(false)}
                  className="p-2 rounded-full active:bg-gray-100"
                  hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                >
                  <Close height={2} width={2} />
                </Pressable>
              </View>

              <View className="flex-1 px-4 pt-3">
                <View className="flex-row justify-between items-center mb-3">
                  <Text className="text-gray-700 text-sm font-medium">
                    Choose a professional prompt:
                  </Text>
                  <Pressable
                    onPress={handleRefreshPrompts}
                    className="px-3 py-1.5 bg-violet-50 rounded-full border border-violet-200 active:bg-violet-100"
                    hitSlop={{ top: 4, bottom: 4, left: 4, right: 4 }}
                  >
                    <Text className="text-violet-600 text-xs font-medium">🔄 New Ideas</Text>
                  </Pressable>
                </View>

                <ScrollView
                  className="flex-1"
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={{ paddingBottom: keyboardHeight + 120 }}
                  keyboardShouldPersistTaps="handled"
                  bounces={true}
                >
                  {randomPrompts.map((prompt, index) => (
                    <Pressable
                      key={index}
                      onPress={() => handlePromptSelect(prompt)}
                      className="p-4 bg-gray-50 rounded-xl mb-3 border border-gray-200 active:bg-gray-100"
                    >
                      <Text className="text-gray-800 text-sm leading-5">{prompt}</Text>
                    </Pressable>
                  ))}
                </ScrollView>

                <View
                  className="border-t border-gray-200 pt-3 pb-2 bg-white"
                  style={{ paddingBottom: keyboardHeight > 0 ? keyboardHeight + 8 : 8 }}
                >
                  <View className="flex-row gap-2">
                    <Pressable
                      onPress={() => {
                        setShowAIModal(false);
                        setTimeout(() => setShowCustomPrompt(true), 300);
                      }}
                      className="flex-1 p-3 bg-blue-50 rounded-xl border border-blue-200 active:bg-blue-100"
                    >
                      <Text className="text-blue-600 text-sm font-medium text-center">
                        ✏️ Custom Prompt
                      </Text>
                    </Pressable>

                    <Pressable
                      onPress={() => {
                        const surprisePrompt =
                          'Create an engaging and informative post about innovative maritime technologies and their transformative impact on sustainable shipping practices and environmental conservation';
                        handlePromptSelect(surprisePrompt);
                      }}
                      className="flex-1 p-3 bg-violet-50 rounded-xl border border-violet-200 active:bg-violet-100"
                    >
                      <Text className="text-violet-600 text-sm font-medium text-center">
                        🎲 Surprise Me
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </View>
            </View>
          </KeyboardAvoidingView>
        </Modal>

        <CustomPromptModal
          visible={showCustomPrompt}
          onClose={() => setShowCustomPrompt(false)}
          onSubmit={handleCustomPrompt}
        />
        <RateLimitModal visible={showRateLimitModal} onClose={() => setShowRateLimitModal(false)} />
        
        <BottomSheet
          visible={isAttachmentOptionsVisible}
          onClose={() => setIsAttachmentOptionsVisible(false)}
          onModalHide={handleModalHide}
          height={140}
        >
          <OptionsMenu>
            <OptionItem label="Gallery" onPress={handleGalleryPress} />
            <View className="h-[1px] bg-gray-200" />
            <OptionItem label="Camera" onPress={handleCameraPress} />
          </OptionsMenu>
        </BottomSheet>
      </View>
    </KeyboardAvoidingView>
  );
};

export default CreatePostForm;
