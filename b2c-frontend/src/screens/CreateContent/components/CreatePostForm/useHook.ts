import { useCallback, useRef, useState } from 'react';
import { Platform, TextInput } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import ImagePicker from 'react-native-image-crop-picker';
import { useDispatch } from 'react-redux';
import {
  createPost,
  createScrapbookPost,
  editPost,
  editScrapbookPost,
} from '@/src/redux/slices/content/contentSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { compressToTargetSize } from '@/src/utilities/upload/compress';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { processVideoForUpload } from '@/src/utilities/upload/videoCompress';
import { fetchPostAPI } from '@/src/networks/content/post';
import type { PostMediaI } from '@/src/networks/content/types';
import { fetchScrapbookPost } from '@/src/networks/port/scrapbook';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { CreatePostFormProps, MediaI } from './types';

const useCreatePostForm = ({
  onSuccess,
  portUnLocode,
  type,
  editing,
  postId,
}: CreatePostFormProps) => {
  const [caption, setCaption] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [media, setMedia] = useState<MediaI[]>([]);
  const [originalMedia, setOriginalMedia] = useState<MediaI[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isCaptioning, setIsCaptioning] = useState(false);
  const [isDone, setIsDone] = useState(false);
  const [postText, setPostText] = useState('');
  const [isAttachmentOptionsVisible, setIsAttachmentOptionsVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<'gallery' | 'camera' | null>(null);

  const textInputRef = useRef<TextInput>(null);
  const dispatch = useDispatch<AppDispatch>();

  const fetchPostData = async () => {
    if (!postId) return;
    try {
      setIsLoading(true);
      if (type === 'SCRAPBOOK_POST') {
        const scrapbookPostData = await fetchScrapbookPost(postId);
        setCaption(scrapbookPostData.text || '');
      } else {
        const postData = await fetchPostAPI(postId);
        setCaption(postData.caption || '');
        if (postData.Media && postData.Media.length > 0) {
          const mediaItems: MediaI[] = postData.Media.map((mediaItem, index) => ({
            uri: mediaItem.fileUrl,
            type: `image/${mediaItem.extension}`,
            filename: `media-${index}.${mediaItem.extension}`,
            caption: mediaItem.caption || undefined,
          }));

          setMedia(mediaItems);
          setOriginalMedia(mediaItems);
          setSelectedImage(mediaItems[0]?.uri || null);
          setIsDone(true);
        }
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Failed to fetch post data',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (editing && postId) {
        fetchPostData();
      }
    }, [editing, postId]),
  );

  const handleAttachments = () => {
    const remainingSlots = 7 - media.length;
    if (remainingSlots <= 0) {
      showToast({
        message: 'Limit reached',
        description: 'You can only upload up to 7 media files.',
        type: 'info',
      });
      return;
    }
    
    setIsAttachmentOptionsVisible(true);
  };

  const handleModalHide = () => {
    if (!pendingAction) return;
    
    setTimeout(() => {
      if (pendingAction === 'gallery') {
        openGallery();
      } else if (pendingAction === 'camera') {
        openCamera();
      }
      setPendingAction(null);
    }, 100);
  };

  const openGallery = async () => {
    try {
      const remainingSlots = 7 - media.length;
      
      const mediaFiles = await ImagePicker.openPicker({
        multiple: true,
        mediaType: 'any', // Allow both photos and videos
        maxFiles: remainingSlots,
      });

      if (!mediaFiles || !Array.isArray(mediaFiles)) return;

      const limitedFiles = mediaFiles.slice(0, remainingSlots);

      const processedFiles: MediaI[] = [];

      for (let i = 0; i < limitedFiles.length; i++) {
        const file = limitedFiles[i];
        const isVideo = file.mime?.startsWith('video/') || false;

        if (isVideo) {
          // Process video
          const videoResult = await processVideoForUpload(file.path, file.filename, file.mime);

          if (!videoResult.isValid) {
            showToast({
              message: 'Video Error',
              description: videoResult.errorMessage || 'Invalid video file',
              type: 'error',
            });
            continue; // Skip this file
          }

          processedFiles.push({
            uri: videoResult.uri,
            type: videoResult.type,
            filename: videoResult.filename,
          });
        } else {
          // Process image
          const compressedUri = await compressToTargetSize(file.path, 500);
          processedFiles.push({
            uri: compressedUri,
            type: 'image/jpeg',
            filename: file.filename ?? `File-${i}.jpg`,
          });
        }
      }

      if (selectedImage && postText) {
        setMedia((prev) =>
          prev.map((item) => (item.uri === selectedImage ? { ...item, caption: postText } : item)),
        );
      }

      setMedia((prev) => [...prev, ...processedFiles]);
      setSelectedImage(processedFiles[0]?.uri ?? null);
      setPostText('');
      setIsCaptioning(true);
      setIsDone(false);
    } catch (error: unknown) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'message' in error &&
        typeof (error as { message: unknown }).message === 'string' &&
        (error as { message: string }).message.toLowerCase().includes('cancel')
      ) {
        return;
      }
      showToast({
        message: 'Error',
        description: 'Media selection failed',
        type: 'error',
      });
    }
  };

  const handleDone = () => {
    if (selectedImage) {
      setMedia((prev) =>
        prev.map((item) => (item.uri === selectedImage ? { ...item, caption: postText } : item)),
      );
    }
    setIsCaptioning(false);
    setIsDone(true);
    setPostText('');
  };

  const handlePostThumbnail = (url: string) => {
    if (selectedImage && postText) {
      setMedia((prev) =>
        prev.map((item) => (item.uri === selectedImage ? { ...item, caption: postText } : item)),
      );
    }

    const existing = media.find((item) => item.uri === url);
    setPostText(existing?.caption || '');
    setSelectedImage(url);
    setIsCaptioning(true);
  };

  const handlePreview = (post: MediaI) => {
    setSelectedImage(post.uri);
    setIsDone(false);
    setPostText(post.caption ?? '');
    setIsCaptioning(true);
  };

  const handleDeletePost = () => {
    const updatedMedia = media.filter((item) => item.uri !== selectedImage);
    if (updatedMedia.length === 0) {
      setMedia([]);
      setSelectedImage(null);
      setPostText('');
      setIsCaptioning(false);
      return;
    }
    const newSelectedImage = updatedMedia[0].uri;
    setMedia(updatedMedia);
    setSelectedImage(newSelectedImage);
    setPostText(updatedMedia[0].caption || '');
  };

  const handlePost = async () => {
    if (!caption.trim() || isSubmitting) return;

    try {
      setIsSubmitting(true);

      if (type === 'SCRAPBOOK_POST') {
        if (editing && postId) {
          await dispatch(
            editScrapbookPost({
              payload: {
                text: caption.trim(),
                portUnLocode: portUnLocode!,
              },
              postId,
            }),
          ).unwrap();
        } else {
          await dispatch(
            createScrapbookPost({
              text: caption.trim(),
              portUnLocode: portUnLocode!,
            }),
          ).unwrap();
        }
      } else {
        let mediaData: PostMediaI[] = [];

        if (editing) {
          const newMedia = media?.filter((file) => file && !file.uri?.startsWith('http')) || [];
          const existingMedia = media?.filter((file) => file && file.uri?.startsWith('http')) || [];

          if (newMedia.length > 0) {
            const extensions = newMedia
              .map((file) => file?.type?.split('/')[1])
              .filter((ext) => ext);

            if (extensions.length > 0) {
              const response = await fetchPresignedUrlAPI(extensions, 'POST');

              if (!Array.isArray(response) || response.length !== newMedia.length) {
                throw new Error('Failed to get upload URLs');
              }

              await Promise.all(
                newMedia.map((file, index) => {
                  const presignedData = response[index];
                  return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
                }),
              );

              const newMediaData: PostMediaI[] = response.map((item, index) => ({
                fileUrl: item.accessUrl,
                caption: newMedia[index]?.caption || null,
                extension: newMedia[index]?.type?.split('/')[1] || 'jpeg',
              }));

              mediaData = [...mediaData, ...newMediaData];
            }
          }

          if (existingMedia.length > 0) {
            const existingMediaData: PostMediaI[] = existingMedia
              .filter((item) => item && originalMedia?.some((orig) => orig?.uri === item.uri))
              .map((item) => ({
                fileUrl: item.uri,
                caption: item.caption || null,
                extension: 'jpeg',
              }));

            mediaData = [...mediaData, ...existingMediaData];
          }
        } else {
          if (media?.length > 0) {
            const extensions = media.map((file) => file?.type?.split('/')[1]).filter((ext) => ext);

            if (extensions.length > 0) {
              const response = await fetchPresignedUrlAPI(extensions, 'POST');

              if (!Array.isArray(response) || response.length !== media.length) {
                throw new Error('Failed to get upload URLs');
              }

              await Promise.all(
                media.map((file, index) => {
                  const presignedData = response[index];
                  return uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
                }),
              );

              mediaData = response.map((item, index) => ({
                fileUrl: item.accessUrl,
                caption: media[index]?.caption || null,
                extension: media[index]?.type?.split('/')[1] || 'jpeg',
              }));
            }
          }
        }

        const payload = {
          caption: caption.trim(),
          files: mediaData,
        };

        if (editing && postId) {
          await dispatch(
            editPost({
              payload,
              postId,
            }),
          ).unwrap();
        } else {
          await dispatch(createPost(payload)).unwrap();
        }
      }

      onSuccess();
      resetForm();
    } catch (error) {
      showToast({
        message: 'Error',
        description: editing
          ? 'Failed to update post'
          : type === 'SCRAPBOOK_POST'
            ? 'Failed to create scrapbook post'
            : 'Failed to create post',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    textInputRef.current?.clear();
    setCaption('');
    setMedia([]);
    setOriginalMedia([]);
    setSelectedImage(null);
    setPostText('');
    setIsCaptioning(false);
    setIsDone(false);
  };
  
  const openCamera = async () => {
    try {
      const image = await ImagePicker.openCamera({
        width: 1000,
        height: 1000,
        cropping: false,
        mediaType: 'photo',
        compressImageQuality: 0.8,
      });
      
      if (!image) return;
      
      const isVideo = image.mime?.startsWith('video/') || false;
      let processedFile: MediaI;
      
      if (isVideo) {
        const videoResult = await processVideoForUpload(image.path, image.filename, image.mime);
        processedFile = {
          uri: videoResult.uri,
          type: videoResult.type,
          filename: videoResult.filename,
          // isVideo: true,
        };
      } else {
        const compressedPath = await compressToTargetSize(image.path, 500);
        processedFile = {
          uri: compressedPath,
          type: 'image/jpeg',
          filename: image.filename || `image_${Date.now()}.jpg`,
          // isVideo: false,
        };
      }
      
      setMedia((prev) => [...prev, processedFile]);
    } catch (error) {
      if (error && typeof error === 'object' && 'code' in error && error.code !== 'E_PICKER_CANCELLED') {
        showToast({
          message: 'Error',
          description: 'Failed to capture image',
          type: 'error',
        });
      }
    }
  };
  
  const handleGalleryPress = () => {
    setPendingAction('gallery');
    setIsAttachmentOptionsVisible(false);
  };
  
  const handleCameraPress = () => {
    setPendingAction('camera');
    setIsAttachmentOptionsVisible(false);
  };

  return {
    caption,
    setCaption,
    postText,
    setPostText,
    isSubmitting,
    isLoading,
    isCaptioning,
    setIsCaptioning,
    isDone,
    selectedImage,
    handlePostThumbnail,
    handleDeletePost,
    handlePreview,
    handleDone,
    handlePost,
    handleAttachments,
    textInputRef,
    media,
    setMedia,
    isAttachmentOptionsVisible,
    setIsAttachmentOptionsVisible,
    handleModalHide,
    handleGalleryPress,
    handleCameraPress,
  };
};

export default useCreatePostForm;
