/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ProfileForDataI } from '@/src/networks/question/types';
import type { ForumQuestionI, TopicI } from '@/src/networks/question/types';

type Topic = { id: string; label: string };
type Equipment = { id: string; label: string };

export type PreviewIconType = 'photo' | 'pdf' | 'video' | 'excel' | 'link';

export type AttachmentI = {
  fileUrl: string;
  fileExtension: string;
};

type Media = {
  id: string;
  fileUrl: string;
  fileExtension: 'webp' | 'jpeg' | 'jpg' | 'pdf' | 'xls' | 'xlsx';
};

export type ForumPostProps = {
  postId: string;
  communityId: string;
  profile: ProfileForDataI | null;
  type: 'troubleshooting' | 'question';
  topics?: Topic[];
  equipment?: Equipment[];
  title: string;
  isSolved: boolean;
  description?: string;
  previewIcons?: PreviewIconType[];
  upVotes: number;
  downVotes: number;
  answers: number;
  commentCount: number;
  community?: string;
  endTime: number;
  answerView: boolean;
  attachments?: AttachmentI[];
  canModify?: boolean;
  isLive: boolean;
  isAnonymous?: boolean;
  highlightText?: string;
  onPress?: () => void;
};

export type ForumQuestionPostProps = {
  question: ForumQuestionI;
  attachments?: AttachmentI[];
};

export type FileViewerModalProps = {
  isVisible: boolean;
  onClose: () => void;
  attachments: AttachmentI[];
  initialIndex?: number;
};
