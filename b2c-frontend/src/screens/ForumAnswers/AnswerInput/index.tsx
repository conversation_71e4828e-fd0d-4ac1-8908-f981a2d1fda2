/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  Pressable,
  ActivityIndicator,
  Text,
  Keyboard,
  Platform,
  Animated,
} from 'react-native';
import BottomSheet from '@/src/components/Bottomsheet';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import Attachment from '@/src/assets/svgs/Attachment';
import Send from '@/src/assets/svgs/Send';
import AttachmentPreview from './components/AttachmentPreview';
import type { AnswerInputPropsI } from './types';
import { MAX_ANSWER_LENGTH, useAnswerInput } from './useHook';

const AnswerInput = ({ onSubmit }: AnswerInputPropsI) => {
  const {
    answer,
    setAnswer,
    isSubmitting,
    attachments,
    inputRef,
    canSubmit,
    isNearLimit,
    handleAttachments,
    handleDeleteAttachment,
    handleSend,
    isAttachmentOptionsVisible,
    setIsAttachmentOptionsVisible,
    handleModalHide,
    handleGalleryPress,
    handleFilesPress,
    handleCameraPress,
  } = useAnswerInput(onSubmit);
  const translateY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      if (Platform.OS === 'android') {
        const shouldApplyKeyboardAdjustment = Platform.Version >= 35;
        if (shouldApplyKeyboardAdjustment) {
          Animated.timing(translateY, {
            toValue: -e.endCoordinates.height,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      }
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (Platform.OS === 'android') {
        const shouldApplyKeyboardAdjustment = Platform.Version >= 35;
        if (shouldApplyKeyboardAdjustment) {
          Animated.timing(translateY, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      }
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, [translateY]);

  return (
    <Animated.View
      className="bg-white border-t border-gray-200 p-4"
      style={{
        paddingBottom: 8,
        transform: [{ translateY }],
      }}
    >
      {attachments.length > 0 && (
        <AttachmentPreview attachments={attachments} onRemove={handleDeleteAttachment} />
      )}
      <View className="flex-row items-start gap-3">
        <View className="flex-1">
          <View className="flex relative justify-between">
            <TextInput
              ref={inputRef}
              className="bg-gray-100 rounded-2xl text-black px-4 py-3 pr-12 text-base min-h-[44px] max-h-[120px]"
              placeholder="Write your answer..."
              placeholderTextColor="#9CA3AF"
              value={answer}
              onChangeText={setAnswer}
              maxLength={MAX_ANSWER_LENGTH}
              multiline
              autoCorrect
              editable={!isSubmitting}
              textAlignVertical="top"
            />
            <View className="right-4">
              <Pressable
                onPress={handleAttachments}
                className="absolute bottom-2 right-4 mr-4 w-8 h-8 rounded-full items-center justify-center bg-gray-200"
              >
                <Attachment width={3} height={3} />
              </Pressable>
            </View>
            <Pressable
              onPress={handleSend}
              disabled={!canSubmit}
              className={`absolute right-2 bottom-2 w-8 h-8 rounded-full items-center justify-center ${
                canSubmit ? 'bg-green-800' : 'bg-gray-300'
              }`}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Send width={1.6} height={1.6} color="white" strokeWidth={2} />
              )}
            </Pressable>
          </View>
          <View className="flex-row justify-start items-center mt-2 px-2">
            <Text className={`text-xs ${isNearLimit ? 'text-orange-500' : 'text-gray-500'}`}>
              {answer.length}/{MAX_ANSWER_LENGTH}
            </Text>
          </View>
        </View>
      </View>
      
      <BottomSheet
        visible={isAttachmentOptionsVisible}
        onClose={() => setIsAttachmentOptionsVisible(false)}
        onModalHide={handleModalHide}
        height={Platform.OS === 'ios' ? 180 : 140}
      >
        <OptionsMenu>
          {Platform.OS === 'ios' && (
            <>
              <OptionItem label="Photos" onPress={handleGalleryPress} />
              <View className="h-[1px] bg-gray-200" />
            </>
          )}
          <OptionItem label="Files" onPress={handleFilesPress} />
          <View className="h-[1px] bg-gray-200" />
          <OptionItem label="Camera" onPress={handleCameraPress} />
        </OptionsMenu>
      </BottomSheet>
    </Animated.View>
  );
};

export default AnswerInput;
