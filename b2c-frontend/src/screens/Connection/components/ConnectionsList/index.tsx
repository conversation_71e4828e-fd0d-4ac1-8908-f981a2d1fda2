import React from 'react';
import { useState, useMemo } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import Close from '@/src/assets/svgs/Close';
import Tick from '@/src/assets/svgs/Tick';
import { ConnectionsListProps } from './types';
import { useConnections } from './useHook';

const ConnectionsList: React.FC<ConnectionsListProps> = ({ profileId, type, onUserPress }) => {
  const { data, loading, loadMore, refresh, refreshing, handleConnectionRequest, requestStatus } =
    useConnections({
      profileId,
      type,
    });

  const [modalVisible, setModalVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    item: ListItem | null;
    status: 'ACCEPTED' | 'REJECTED' | null;
  }>({ item: null, status: null });
  const [isConfirming, setIsConfirming] = useState(false);

  const openConfirmModal = (item: ListItem, status: 'ACCEPTED' | 'REJECTED') => {
    setPendingAction({ item, status });
    setModalVisible(true);
  };

  const closeConfirmModal = () => {
    setModalVisible(false);
  };

  const confirmAction = async () => {
    if (!pendingAction.item || !pendingAction.status) return;
    const profileId = pendingAction.item.Profile?.id;
    if (!profileId) return;

    setIsConfirming(true);
    try {
      await handleConnectionRequest(pendingAction.item, pendingAction.status);
    } finally {
      setIsConfirming(false);
      closeConfirmModal();
    }
  };

  const renderActions = (item: ListItem) => {
    if (type !== 'requests_received') return null;

    const profileId = item.Profile?.id;
    if (!profileId) return null;
    const status = requestStatus[profileId];

    if (status === 'ACCEPTED') {
      return (
        <View className="my-3">
          <Text className="text-primaryGreen font-semibold">Request Accepted</Text>
        </View>
      );
    }

    if (status === 'REJECTED') {
      return (
        <View className="my-3">
          <Text className="text-red-600 font-semibold">Request Rejected</Text>
        </View>
      );
    }

    return (
      <View className="flex-row my-3">
        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            openConfirmModal(item, 'ACCEPTED');
          }}
          className="py-2 px-3 bg-primaryGreen rounded-lg mr-2"
        >
          <View className="flex-row items-center gap-1">
            <Tick width={1.75} height={1.75} color="white" />
            <Text className="text-white">Accept</Text>
          </View>
        </Pressable>

        <Pressable
          onPress={(e) => {
            e.stopPropagation();
            openConfirmModal(item, 'REJECTED');
          }}
          className="py-2 px-3 bg-red-700 rounded-lg"
        >
          <View className="flex-row items-center gap-1">
            <Close width={1.75} height={1.75} color="white" />
            <Text className="text-white">Reject</Text>
          </View>
        </Pressable>
      </View>
    );
  };

  const renderCustom = (item: ListItem) => {
    const { status } = item;

    if (!status || type !== 'requests_sent') return null;

    const baseStyles = 'px-3 py-1 rounded-full border';
    let style = '';
    let textColor = 'text-black';

    switch (status.toUpperCase()) {
      case 'PENDING':
        style = 'border-yellow-500';
        textColor = 'text-yellow-700';
        break;
      case 'REVOKED':
        style = 'border-orange-500';
        textColor = 'text-orange-500';
        break;
      case 'REJECTED':
        style = 'border-red-500';
        textColor = 'text-red-500';
        break;
      case 'ACCEPTED':
        style = 'border-green-500';
        textColor = 'text-green-500';
        break;
      default:
        style = 'bg-gray-200 border-gray-200';
    }

    return (
      <View className={`${baseStyles} ${style}`}>
        <Text className={`${textColor} font-medium text-sm`}>{capitalizeFirstLetter(status)}</Text>
      </View>
    );
  };

  const getModalContent = () => {
    if (!pendingAction.item || !pendingAction.status) return { title: '', description: '' };

    const userName = pendingAction.item.Profile?.name || 'this user';

    if (pendingAction.status === 'ACCEPTED') {
      return {
        title: 'Accept Connection Request',
        description: `Are you sure you want to accept the connection request from ${userName}?`,
        confirmText: 'Accept',
      };
    } else {
      return {
        title: 'Reject Connection Request',
        description: `Are you sure you want to reject the connection request from ${userName}?`,
        confirmText: 'Reject',
      };
    }
  };

  const modalContent = getModalContent();

  const transformedData = useMemo(() => {
    return data.map((item) => ({ Profile: item.Profile, status: item.status }));
  }, [data]);

  if (loading && !data.length) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (!data.length) {
    return (
      <NotFound
        title={`No ${type.replace('_', ' ')} found`}
        subtitle="When you have connections, they will appear here"
      />
    );
  }

  return (
    <>
      <UsersList
        key={type}
        data={transformedData}
        loading={loading}
        onLoadMore={loadMore}
        onPress={onUserPress}
        onRefresh={refresh}
        refreshing={refreshing}
        renderActions={renderActions}
        renderCustom={renderCustom}
      />

      <CustomModal
        isVisible={modalVisible}
        title={modalContent.title}
        description={modalContent.description}
        confirmText={modalContent.confirmText}
        confirmButtonVariant={modalContent.confirmText === 'Accept' ? 'default' : 'danger'}
        cancelText="Cancel"
        onConfirm={confirmAction}
        onCancel={closeConfirmModal}
        isConfirming={isConfirming}
      />
    </>
  );
};

export default ConnectionsList;
