import { useState, useEffect, useRef, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { showToast } from '@/src/utilities/toast';
import { fetchConnectionsAPI, fetchMutualsAPI } from '@/src/networks/connect/connection';
import { fetchFollowersAPI, fetchFollowingAPI } from '@/src/networks/connect/follow';
import {
  fetchReceivedRequestsAPI,
  fetchSentRequestsAPI,
  respondReceivedRequestAPI,
} from '@/src/networks/connect/request';
import { FetchFollowersDataI, FetchFollowersResponseI } from '@/src/networks/connect/types';
import { UseConnectionsProps } from './types';

const CURSOR_BASED_TYPES = ['followers', 'following', 'mutuals', 'connections'];

export const useConnections = ({ profileId, type, pageSize = 10 }: UseConnectionsProps) => {
  const [data, setData] = useState<FetchFollowersDataI[]>([]);
  const [cursorId, setCursorId] = useState<number | null>(null);
  const [page, setPage] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [requestStatus, setRequestStatus] = useState<
    Record<string, 'ACCEPTED' | 'REJECTED' | undefined>
  >({});
  const [total, setTotal] = useState<number>(0);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const prevTypeRef = useRef(type);
  const isCursorBased = CURSOR_BASED_TYPES.includes(type);

  const fetchData = useCallback(async (isRefresh = false) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    if (isRefresh) {
      setRefreshing(true);
    } else if (!isRefresh && !data.length) {
      setLoading(true);
    }
    try {
      let result: FetchFollowersResponseI;

      switch (type) {
        case 'followers':
          result = await fetchFollowersAPI({
            profileId,
            cursorId: isRefresh ? null : cursorId,
            pageSize,
          });
          break;
        case 'following':
          result = await fetchFollowingAPI({
            profileId,
            cursorId: isRefresh ? null : cursorId,
            pageSize,
          });
          break;
        case 'mutuals':
          result = await fetchMutualsAPI({
            profileId,
            cursorId: isRefresh ? null : cursorId,
            pageSize,
          });
          break;
        case 'connections':
          result = await fetchConnectionsAPI({
            profileId,
            cursorId: isRefresh ? null : cursorId,
            pageSize,
          });
          break;
        case 'requests_sent':
          result = await fetchSentRequestsAPI({
            page: isRefresh ? 0 : page,
            pageSize,
          });
          break;
        case 'requests_received':
          result = await fetchReceivedRequestsAPI({
            page: isRefresh ? 0 : page,
            pageSize,
          });
          break;
        default:
          throw new Error(`Unknown connection type: ${type}`);
      }

      const newData = result.data || [];
      const totalCount = result.total || 0;
      setTotal(totalCount);

      if (isCursorBased) {
        const minCursorItem =
          newData.length > 0
            ? newData.reduce((max, item) => (item.cursorId < max.cursorId ? item : max), newData[0])
            : null;
        const newCursorId = minCursorItem?.cursorId ?? null;
        const updatedData = isRefresh ? newData : [...data, ...newData];
        setHasMore(updatedData.length < totalCount);
        setCursorId(newCursorId);
        setData(updatedData);
      } else {
        const existingIds = new Set(data.map((item) => item.Profile.id));
        const filteredNew = newData.filter((item) => !existingIds.has(item.Profile.id));
        const updatedData = isRefresh ? newData : [...data, ...filteredNew];
        setHasMore(updatedData.length < totalCount);
        if (isRefresh) {
          setPage(1);
        } else if (filteredNew.length > 0 && updatedData.length < totalCount) {
          setPage((prev) => prev + 1);
        }
        setData(updatedData);
      }
    } catch (err) {
      const errorMsg = `Error fetching ${type}`;
      if (!data.length && !isRefresh) {
        triggerErrorBoundary(
          new Error(
            'Failed to load initial data: ' +
              (err instanceof Error ? err.message : 'Unknown error'),
          ),
        );
      } else {
        showToast({
          type: 'error',
          message: 'Error',
          description: errorMsg,
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [type, profileId, data, cursorId, page, pageSize, isCursorBased]);

  const handleConnectionRequest = async (item: ListItem, status: 'ACCEPTED' | 'REJECTED') => {
    const profileId = item.Profile?.id;
    if (!profileId) return;

    try {
      await respondReceivedRequestAPI({
        requestedStatus: status,
        senderProfileId: profileId,
      });
      setRequestStatus((prev) => ({ ...prev, [profileId]: status }));
      showToast({
        type: 'success',
        message: 'Success',
        description:
          status === 'ACCEPTED'
            ? 'Connection request accepted successfully.'
            : 'Connection request rejected successfully.',
      });
    } catch {
      showToast({
        type: 'error',
        message: 'Error',
        description:
          status === 'ACCEPTED' ? 'Failed to accept request' : 'Failed to reject request',
      });
    }
  };

  useEffect(() => {
    if (prevTypeRef.current !== type) {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      setLoading(true);
      setData([]);
      setCursorId(null);
      setPage(0);
      setTotal(0);
      setHasMore(false);
      setRequestStatus({});
      setRefreshing(false);
      prevTypeRef.current = type;
    }
    fetchData(true);
  }, [type, profileId]);

  useEffect(() => {
    if (data && Array.isArray(data)) {
      if (type === 'requests_received') {
        const initialStatus: Record<string, 'ACCEPTED' | 'REJECTED'> = {};
        data.forEach((item) => {
          const profileId = item.Profile?.id;
          if (profileId && (item.status === 'ACCEPTED' || item.status === 'REJECTED')) {
            initialStatus[profileId] = item.status;
          }
        });
        setRequestStatus(initialStatus);
      } else {
        setRequestStatus({});
      }
    }
  }, [data, type]);

  const loadMore = () => {
    if (!loading && !refreshing && hasMore && total > pageSize) {
      fetchData(false);
    }
  };

  const refresh = () => {
    fetchData(true);
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    loading,
    refreshing,
    hasMore,
    loadMore,
    refresh,
    handleConnectionRequest,
    requestStatus,
    total,
  };
};

export default useConnections;
