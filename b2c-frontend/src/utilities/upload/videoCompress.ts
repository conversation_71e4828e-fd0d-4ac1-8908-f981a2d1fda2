/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Video, getVideoMetaData } from 'react-native-compressor';
import { showToast } from '../toast';

const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB

// Supported video formats
const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/mov',
  'video/quicktime',
  'video/avi',
  'video/x-msvideo',
  'video/3gpp',
  'video/webm',
];

export interface VideoInfo {
  uri: string;
  duration: number;
  size: number;
  width: number;
  height: number;
}

export const getVideoInfo = async (uri: string): Promise<VideoInfo> => {
  try {
    const info = await getVideoMetaData(uri);
    return {
      uri,
      duration: info.duration || 0,
      size: info.size || 0,
      width: info.width || 0,
      height: info.height || 0,
    };
  } catch (error) {
    throw new Error('Failed to get video information');
  }
};

export const validateVideoSize = (size: number): boolean => {
  return size <= MAX_VIDEO_SIZE;
};

export const validateVideoType = (mimeType: string): boolean => {
  return SUPPORTED_VIDEO_TYPES.includes(mimeType.toLowerCase());
};

export const getVideoTypeFromUri = (uri: string): string => {
  const extension = uri.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'mp4':
      return 'video/mp4';
    case 'mov':
      return 'video/mov';
    case 'avi':
      return 'video/avi';
    case '3gp':
      return 'video/3gpp';
    case 'webm':
      return 'video/webm';
    default:
      return 'video/mp4'; // Default fallback
  }
};

export const compressVideo = async (uri: string, targetSizeMB: number = 10): Promise<string> => {
  try {
    const targetSizeBytes = targetSizeMB * 1024 * 1024;

    const compressedUri = await Video.compress(
      uri,
      {
        compressionMethod: 'auto',
        maxSize: targetSizeBytes,
        minimumFileSizeForCompress: 2 * 1024 * 1024, // 2MB
      },
      (progress) => {
        // Progress callback - could be used for progress indicator
        console.log('Compression Progress: ', progress);
      },
    );

    return compressedUri;
  } catch (error) {
    console.error('Video compression failed:', error);
    throw new Error('Failed to compress video');
  }
};

export const processVideoForUpload = async (
  uri: string,
  filename?: string,
  mimeType?: string,
): Promise<{
  uri: string;
  type: string;
  filename: string;
  duration: number;
  isValid: boolean;
  errorMessage?: string;
}> => {
  try {
    // Get video information
    const videoInfo = await getVideoInfo(uri);
    
    // Determine video type
    const videoType = mimeType || getVideoTypeFromUri(uri);
    const videoFilename = filename || `video.${videoType.split('/')[1]}`;

    // Validate video type
    if (!validateVideoType(videoType)) {
      return {
        uri,
        type: videoType,
        filename: videoFilename,
        duration: videoInfo.duration,
        isValid: false,
        errorMessage: `Unsupported video format. Please use MP4, MOV, AVI, 3GP, or WebM format.`,
      };
    }

    // Validate size
    if (!validateVideoSize(videoInfo.size)) {
      return {
        uri,
        type: videoType,
        filename: videoFilename,
        duration: videoInfo.duration,
        isValid: false,
        errorMessage: `Video size must be 50MB or less. Current size: ${Math.round(videoInfo.size / (1024 * 1024))}MB`,
      };
    }

    let processedUri = uri;

    // Compress if video is larger than 25MB to try to keep it under 50MB
    if (videoInfo.size > 25 * 1024 * 1024) {
      showToast({
        type: 'info',
        message: 'Compressing Video',
        description: 'Please wait while we optimize your video...',
      });

      try {
        // Try to compress to 25MB to leave some buffer under the 50MB limit
        processedUri = await compressVideo(uri, 25);
      } catch (compressionError) {
        console.error('Video compression failed:', compressionError);
        // If compression fails, still allow the original video if it's under 50MB
        if (videoInfo.size <= MAX_VIDEO_SIZE) {
          showToast({
            type: 'info',
            message: 'Compression Failed',
            description: 'Using original video. Upload may take longer.',
          });
        } else {
          return {
            uri,
            type: videoType,
            filename: videoFilename,
            duration: videoInfo.duration,
            isValid: false,
            errorMessage: 'Video is too large and compression failed. Please use a smaller video.',
          };
        }
      }
    }

    return {
      uri: processedUri,
      type: videoType,
      filename: videoFilename,
      duration: videoInfo.duration,
      isValid: true,
    };
  } catch (error) {
    console.error('Video processing failed:', error);
    const videoType = mimeType || getVideoTypeFromUri(uri);
    const videoFilename = filename || `video.${videoType.split('/')[1]}`;
    
    return {
      uri,
      type: videoType,
      filename: videoFilename,
      duration: 0,
      isValid: false,
      errorMessage: 'Failed to process video. Please check the file format and try again.',
    };
  }
};
